package com.gmail.sync667.gougouserver.server.packets;

import java.net.InetAddress;

import com.gmail.sync667.gougouserver.GouGouServer;

public class Packet13StaminaUpdate extends Packet {

    private int entityId;
    private int stamina;
    private int maxStamina;

    /**
     * Server to Client packet
     */
    public Packet13StaminaUpdate(InetAddress senderIp, int port, int entityId, int stamina, int maxStamina) {
        super(13, senderIp, port);
        this.entityId = entityId;
        this.stamina = stamina;
        this.maxStamina = maxStamina;
    }

    /**
     * Client receiving packet
     */
    public Packet13StaminaUpdate(byte[] data) {
        super(13, null, 0);
        String[] dataArray = readData(data).split("/");
        try {
            this.entityId = Integer.valueOf(dataArray[0]);
            this.stamina = Integer.valueOf(dataArray[1]);
            this.maxStamina = Integer.valueOf(dataArray[2]);
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            this.entityId = 0;
            this.stamina = 0;
            this.maxStamina = 100;
        }
    }

    @Override
    public void writeData(GouGouServer server) {
        // Server sends this packet, no need to implement
    }

    @Override
    public byte[] getData() {
        return ("13" + entityId + "/" + stamina + "/" + maxStamina).getBytes();
    }

    // Getters
    public int getEntityId() {
        return entityId;
    }

    public int getStamina() {
        return stamina;
    }

    public int getMaxStamina() {
        return maxStamina;
    }
}
