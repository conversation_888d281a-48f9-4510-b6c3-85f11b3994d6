package com.gmail.sync667.gougouserver.server.packets;

import java.net.InetAddress;

import com.gmail.sync667.gougouserver.GouGouServer;

public class Packet12HealthUpdate extends Packet {

    private int entityId;
    private int health;
    private int maxHealth;

    /**
     * Server to Client packet
     */
    public Packet12HealthUpdate(InetAddress senderIp, int port, int entityId, int health, int maxHealth) {
        super(12, senderIp, port);
        this.entityId = entityId;
        this.health = health;
        this.maxHealth = maxHealth;
    }

    /**
     * Client receiving packet
     */
    public Packet12HealthUpdate(byte[] data) {
        super(12, null, 0);
        String[] dataArray = readData(data).split("/");
        try {
            this.entityId = Integer.valueOf(dataArray[0]);
            this.health = Integer.valueOf(dataArray[1]);
            this.maxHealth = Integer.valueOf(dataArray[2]);
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            this.entityId = 0;
            this.health = 0;
            this.maxHealth = 100;
        }
    }

    @Override
    public void writeData(GouGouServer server) {
        // Server sends this packet, no need to implement
    }

    @Override
    public byte[] getData() {
        return ("12" + entityId + "/" + health + "/" + maxHealth).getBytes();
    }

    // Getters
    public int getEntityId() {
        return entityId;
    }

    public int getHealth() {
        return health;
    }

    public int getMaxHealth() {
        return maxHealth;
    }
}
