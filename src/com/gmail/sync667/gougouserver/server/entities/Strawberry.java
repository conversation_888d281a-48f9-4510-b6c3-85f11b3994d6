package com.gmail.sync667.gougouserver.server.entities;

import java.net.InetAddress;

import com.gmail.sync667.gougouserver.GouGouServer;
import com.gmail.sync667.gougouserver.server.entities.player.Player;

public class Strawberry extends Entity {
    
    private int animationTimer = 0;
    private boolean isConsumed = false;
    private int healAmount = 25;
    private int staminaAmount = 30;
    private int respawnTimer = 0;
    private static final int RESPAWN_TIME = 1800; // 30 seconds at 60 FPS
    private String name = "Strawberry";
    
    public Strawberry(int entityId, int x, int y) {
        super(entityId);
        this.x = x;
        this.y = y;
    }
    
    @Override
    public void tick() {
        animationTimer++;
        
        if (isConsumed) {
            respawnTimer++;
            if (respawnTimer >= RESPAWN_TIME) {
                // Respawn the strawberry
                isConsumed = false;
                respawnTimer = 0;
                System.out.println("Strawberry respawned at (" + x + ", " + y + ")");
                
                // Send respawn packet to all clients
                // TODO: Implement strawberry respawn packet
            }
        } else {
            // Check for nearby players to heal
            checkForPlayerInteraction();
        }
    }
    
    private void checkForPlayerInteraction() {
        for (Entity entity : GouGouServer.getServer().entities) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                int dx = Math.abs(player.x - this.x);
                int dy = Math.abs(player.y - this.y);
                
                if (dx < 8 && dy < 8) { // Within 8 pixels
                    healPlayer(player);
                    break; // Only heal one player at a time
                }
            }
        }
    }
    
    public void healPlayer(Player player) {
        if (!isConsumed && player != null) {
            // Heal the player
            player.heal(healAmount);
            player.restoreStamina(staminaAmount);
            
            // Mark as consumed
            isConsumed = true;
            respawnTimer = 0;
            
            System.out.println(player.getPlayerName() + " consumed a strawberry! +" + healAmount + " HP, +" + staminaAmount + " Stamina");
            
            // Send consumption packet to all clients
            // TODO: Implement strawberry consumption packet
            createHealingEffect(player);
        }
    }
    
    private void createHealingEffect(Player player) {
        // This could send particle effects to clients
        System.out.println("✨ Healing effect for " + player.getPlayerName() + " at (" + x + ", " + y + ")");
        
        // TODO: Send healing effect packet to all clients
    }
    
    public boolean isAvailable() {
        return !isConsumed;
    }
    
    public int getTimeUntilRespawn() {
        if (!isConsumed) return 0;
        return RESPAWN_TIME - respawnTimer;
    }
    
    public float getRespawnProgress() {
        if (!isConsumed) return 1.0f;
        return (float) respawnTimer / RESPAWN_TIME;
    }
    
    @Override
    public InetAddress getIpAddress() {
        return null; // NPCs don't have IP addresses
    }
    
    @Override
    public int getPort() {
        return 0; // NPCs don't have ports
    }
    
    @Override
    public int getSpeed() {
        return 0; // Strawberries don't move
    }
    
    @Override
    public void setSpeed(int speed) {
        // Strawberries don't move
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public int getMovingDir() {
        return 0; // Strawberries don't move
    }
    
    @Override
    public void setMovingDir(int movingDir) {
        // Strawberries don't move
    }
    
    @Override
    public String getPlayerName() {
        return null; // NPCs don't have player names
    }
    
    // Getters and setters for customization
    public int getHealAmount() { return healAmount; }
    public void setHealAmount(int amount) { this.healAmount = amount; }
    public int getStaminaAmount() { return staminaAmount; }
    public void setStaminaAmount(int amount) { this.staminaAmount = amount; }
    public boolean isConsumed() { return isConsumed; }
    
    public void forceRespawn() {
        isConsumed = false;
        respawnTimer = 0;
    }
}
