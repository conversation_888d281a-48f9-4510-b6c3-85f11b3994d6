package com.gmail.sync667.gougouserver.server.entities.player;

import java.net.InetAddress;

import com.gmail.sync667.gougouserver.GouGouServer;
import com.gmail.sync667.gougouserver.server.entities.Mob;
import com.gmail.sync667.gougouserver.server.packets.Packet12HealthUpdate;
import com.gmail.sync667.gougouserver.server.packets.Packet13StaminaUpdate;

public class Player extends Mob {

    protected boolean isSwimming = false;
    private int tickCount = 0;
    public int scale = 1;
    private final String username;
    private final InetAddress ipAddress;
    private final int port;

    // Health and Stamina System
    public static final int MAX_HEALTH = 100;
    public static final int MAX_STAMINA = 100;
    private int health = MAX_HEALTH;
    private int stamina = MAX_STAMINA;
    private int staminaRegenTimer = 0;
    private int healthRegenTimer = 0;
    private boolean isRunning = false;
    private int invulnerabilityTimer = 0; // Prevents rapid damage

    public Player(int entityId, int x, int y, String username, InetAddress ipAddress, int port) {
        super(entityId, "Gracz", x, y, speed);
        this.username = username;
        this.ipAddress = ipAddress;
        this.port = port;
    }

    @Override
    public void tick() {
        tickCount++;

        // Update health and stamina
        updateHealthAndStamina();

        // Check for entity interactions
        checkEntityInteractions();
    }

    private void updateHealthAndStamina() {
        // Decrease invulnerability timer
        if (invulnerabilityTimer > 0) {
            invulnerabilityTimer--;
        }

        // Stamina regeneration (when not running)
        if (!isRunning) {
            staminaRegenTimer++;
            if (staminaRegenTimer >= 30) { // Regenerate every 0.5 seconds
                if (stamina < MAX_STAMINA) {
                    stamina += 2;
                    if (stamina > MAX_STAMINA) stamina = MAX_STAMINA;
                    // Send stamina update to client
                    sendStaminaUpdate();
                }
                staminaRegenTimer = 0;
            }
        }

        // Health regeneration (slow, when at high stamina)
        if (stamina > 80) {
            healthRegenTimer++;
            if (healthRegenTimer >= 300) { // Regenerate every 5 seconds
                if (health < MAX_HEALTH) {
                    health += 1;
                    if (health > MAX_HEALTH) health = MAX_HEALTH;
                    // Send health update to client
                    sendHealthUpdate();
                }
                healthRegenTimer = 0;
            }
        }
    }

    private void checkEntityInteractions() {
        // Check for collisions with other entities in server entity list
        // This will be implemented when we add server-side entities
    }

    @Override
    public boolean hasCollided(int xa, int ya) {
        return false;

    }

    @Override
    public InetAddress getIpAddress() {
        return ipAddress;
    }

    @Override
    public int getPort() {
        return port;
    }

    @Override
    public int getSpeed() {
        return speed;
    }

    @Override
    public void setSpeed(int speed) {
        Player.speed = speed;
    }

    @Override
    public String getPlayerName() {
        return username;
    }

    // Health and Stamina Methods
    public void takeDamage(int damage) {
        if (invulnerabilityTimer <= 0) {
            health -= damage;
            if (health < 0) health = 0;
            invulnerabilityTimer = 60; // 1 second of invulnerability at 60 FPS

            // Send health update to client
            sendHealthUpdate();

            System.out.println(username + " took " + damage + " damage! Health: " + health + "/" + MAX_HEALTH);

            if (health <= 0) {
                onDeath();
            }
        }
    }

    public void heal(int amount) {
        health += amount;
        if (health > MAX_HEALTH) health = MAX_HEALTH;

        // Send health update to client
        sendHealthUpdate();

        System.out.println(username + " healed " + amount + " HP! Health: " + health + "/" + MAX_HEALTH);
    }

    public void restoreStamina(int amount) {
        stamina += amount;
        if (stamina > MAX_STAMINA) stamina = MAX_STAMINA;

        // Send stamina update to client
        sendStaminaUpdate();

        System.out.println(username + " restored " + amount + " stamina! Stamina: " + stamina + "/" + MAX_STAMINA);
    }

    public void consumeStamina(int amount) {
        stamina -= amount;
        if (stamina < 0) stamina = 0;

        // Send stamina update to client
        sendStaminaUpdate();
    }

    private void onDeath() {
        System.out.println(username + " has died!");
        // Reset position to spawn
        this.x = 80; // spawn position
        this.y = 100;
        health = MAX_HEALTH;
        stamina = MAX_STAMINA;

        // Send respawn packet to client
        sendHealthUpdate();
        sendStaminaUpdate();
        // TODO: Send position update packet
    }

    public boolean canRun() {
        return stamina > 10; // Need at least 10 stamina to run
    }

    public void setRunning(boolean running) {
        this.isRunning = running;
    }

    // Network methods to send updates to client
    private void sendHealthUpdate() {
        GouGouServer.getServer().sendData(ipAddress, port,
            new Packet12HealthUpdate(ipAddress, port, entityId, health, MAX_HEALTH).getData());
    }

    private void sendStaminaUpdate() {
        GouGouServer.getServer().sendData(ipAddress, port,
            new Packet13StaminaUpdate(ipAddress, port, entityId, stamina, MAX_STAMINA).getData());
    }

    // Getters
    public int getHealth() { return health; }
    public int getMaxHealth() { return MAX_HEALTH; }
    public int getStamina() { return stamina; }
    public int getMaxStamina() { return MAX_STAMINA; }
    public boolean isInvulnerable() { return invulnerabilityTimer > 0; }
    public boolean isRunning() { return isRunning; }

}
