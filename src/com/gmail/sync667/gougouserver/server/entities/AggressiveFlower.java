package com.gmail.sync667.gougouserver.server.entities;

import java.net.InetAddress;

import com.gmail.sync667.gougouserver.GouGouServer;
import com.gmail.sync667.gougouserver.server.entities.player.Player;

public class AggressiveFlower extends Entity {
    
    private int attackCooldown = 0;
    private int animationTimer = 0;
    private boolean isAttacking = false;
    private int detectionRange = 24; // 3 tiles
    private int attackRange = 8; // 1 tile
    private int damage = 15;
    private String name = "AggressiveFlower";
    
    public AggressiveFlower(int entityId, int x, int y) {
        super(entityId);
        this.x = x;
        this.y = y;
    }
    
    @Override
    public void tick() {
        animationTimer++;
        
        // Decrease attack cooldown
        if (attackCooldown > 0) {
            attackCooldown--;
        }
        
        // Check for nearby player
        Player player = findNearbyPlayer();
        if (player != null) {
            int dx = player.x - this.x;
            int dy = player.y - this.y;
            double distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance <= attackRange && attackCooldown <= 0) {
                // Attack the player
                attackPlayer(player);
                isAttacking = true;
                attackCooldown = 120; // 2 seconds cooldown at 60 FPS
            } else if (distance <= detectionRange) {
                // Show aggressive animation when player is nearby
                isAttacking = false;
            } else {
                isAttacking = false;
            }
        } else {
            isAttacking = false;
        }
    }
    
    public void attackPlayer(Player player) {
        if (attackCooldown <= 0 && !player.isInvulnerable()) {
            player.takeDamage(damage);
            attackCooldown = 120; // 2 seconds cooldown
            isAttacking = true;
            System.out.println("Aggressive flower attacked " + player.getPlayerName() + " for " + damage + " damage!");
            
            // Send attack animation packet to all clients
            // TODO: Implement attack animation packet
        }
    }
    
    private Player findNearbyPlayer() {
        for (Entity entity : GouGouServer.getServer().entities) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                int dx = Math.abs(player.x - this.x);
                int dy = Math.abs(player.y - this.y);
                double distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance <= detectionRange) {
                    return player;
                }
            }
        }
        return null;
    }
    
    @Override
    public InetAddress getIpAddress() {
        return null; // NPCs don't have IP addresses
    }
    
    @Override
    public int getPort() {
        return 0; // NPCs don't have ports
    }
    
    @Override
    public int getSpeed() {
        return 0; // Flowers don't move
    }
    
    @Override
    public void setSpeed(int speed) {
        // Flowers don't move
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public int getMovingDir() {
        return 0; // Flowers don't move
    }
    
    @Override
    public void setMovingDir(int movingDir) {
        // Flowers don't move
    }
    
    @Override
    public String getPlayerName() {
        return null; // NPCs don't have player names
    }
    
    // Getters for customization
    public int getDamage() { return damage; }
    public void setDamage(int damage) { this.damage = damage; }
    public int getDetectionRange() { return detectionRange; }
    public void setDetectionRange(int range) { this.detectionRange = range; }
    public int getAttackRange() { return attackRange; }
    public void setAttackRange(int range) { this.attackRange = range; }
    public boolean isAttacking() { return isAttacking; }
}
