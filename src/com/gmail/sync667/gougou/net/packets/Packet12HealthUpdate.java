package com.gmail.sync667.gougou.net.packets;

import com.gmail.sync667.gougou.GouGou;
import com.gmail.sync667.gougou.entities.Entity;
import com.gmail.sync667.gougou.entities.player.Player;
import com.gmail.sync667.gougou.net.GouGouClient;

public class Packet12HealthUpdate extends Packet {

    private int entityId;
    private int health;
    private int maxHealth;

    /**
     * Client receiving packet from server
     */
    public Packet12HealthUpdate(byte[] data) {
        super(12);
        String[] dataArray = readData(data).split("/");
        try {
            this.entityId = Integer.valueOf(dataArray[0]);
            this.health = Integer.valueOf(dataArray[1]);
            this.maxHealth = Integer.valueOf(dataArray[2]);
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            this.entityId = 0;
            this.health = 0;
            this.maxHealth = 100;
        }
    }

    @Override
    public void writeData(GouGouClient client) {
        // Client receives this packet, doesn't send it
    }

    @Override
    public byte[] getData() {
        return ("12" + entityId + "/" + health + "/" + maxHealth).getBytes();
    }

    public void handlePacket(GouGouClient client, String username) {
        // Update the health of the specified entity
        if (GouGou.player != null && GouGou.player.getEntityId() == entityId) {
            // Update our own player's health
            GouGou.player.updateHealth(health);
            System.out.println("Health updated: " + health + "/" + maxHealth);
        }
        // Note: Other players' health updates would need access to the game instance
        // For now, we only handle our own player's health
    }

    // Getters
    public int getEntityId() {
        return entityId;
    }

    public int getHealth() {
        return health;
    }

    public int getMaxHealth() {
        return maxHealth;
    }
}
