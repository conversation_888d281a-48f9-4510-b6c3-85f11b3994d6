package com.gmail.sync667.gougou.net.packets;

import com.gmail.sync667.gougou.GouGou;
import com.gmail.sync667.gougou.entities.Entity;
import com.gmail.sync667.gougou.entities.player.Player;
import com.gmail.sync667.gougou.net.GouGouClient;

public class Packet13StaminaUpdate extends Packet {

    private int entityId;
    private int stamina;
    private int maxStamina;

    /**
     * Client receiving packet from server
     */
    public Packet13StaminaUpdate(byte[] data) {
        super(13);
        String[] dataArray = readData(data).split("/");
        try {
            this.entityId = Integer.valueOf(dataArray[0]);
            this.stamina = Integer.valueOf(dataArray[1]);
            this.maxStamina = Integer.valueOf(dataArray[2]);
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            this.entityId = 0;
            this.stamina = 0;
            this.maxStamina = 100;
        }
    }

    @Override
    public void writeData(GouGouClient client) {
        // Client receives this packet, doesn't send it
    }

    @Override
    public byte[] getData() {
        return ("13" + entityId + "/" + stamina + "/" + maxStamina).getBytes();
    }

    public void handlePacket(GouGouClient client, String username) {
        // Update the stamina of the specified entity
        if (GouGou.player != null && GouGou.player.getEntityId() == entityId) {
            // Update our own player's stamina
            GouGou.player.updateStamina(stamina);
            System.out.println("Stamina updated: " + stamina + "/" + maxStamina);
        }
        // Note: Other players' stamina updates would need access to the game instance
        // For now, we only handle our own player's stamina
    }

    // Getters
    public int getEntityId() {
        return entityId;
    }

    public int getStamina() {
        return stamina;
    }

    public int getMaxStamina() {
        return maxStamina;
    }
}
