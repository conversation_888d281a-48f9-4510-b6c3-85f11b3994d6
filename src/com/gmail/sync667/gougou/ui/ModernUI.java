package com.gmail.sync667.gougou.ui;

import com.gmail.sync667.gougou.entities.player.Player;
import com.gmail.sync667.gougou.gfx.ModernRenderer;

import java.awt.*;

public class ModernUI {
    
    private static final int UI_MARGIN = 10;
    private static final int BAR_WIDTH = 120;
    private static final int BAR_HEIGHT = 16;
    private static final int BAR_SPACING = 8;
    private static final int CORNER_RADIUS = 4;
    
    // Modern color scheme
    private static final Color UI_BACKGROUND = new Color(0, 0, 0, 180);
    private static final Color UI_BORDER = new Color(255, 255, 255, 100);
    private static final Color HEALTH_BG = new Color(60, 20, 20, 200);
    private static final Color HEALTH_LOW = new Color(220, 50, 50);
    private static final Color HEALTH_MED = new Color(220, 180, 50);
    private static final Color HEALTH_HIGH = new Color(50, 220, 50);
    private static final Color STAMINA_BG = new Color(20, 20, 60, 200);
    private static final Color STAMINA_FILL = new Color(50, 150, 220);
    private static final Color TEXT_COLOR = new Color(255, 255, 255);
    private static final Color LABEL_COLOR = new Color(200, 200, 200);
    
    // Fonts
    private static java.awt.Font labelFont = new java.awt.Font("Arial", java.awt.Font.BOLD, 11);
    private static java.awt.Font valueFont = new java.awt.Font("Arial", java.awt.Font.PLAIN, 10);
    private static java.awt.Font statusFont = new java.awt.Font("Arial", java.awt.Font.ITALIC, 9);
    
    public static void render(ModernRenderer renderer, Player player) {
        if (player == null) return;
        
        int x = UI_MARGIN;
        int y = UI_MARGIN;
        
        // Calculate UI panel size
        int panelWidth = BAR_WIDTH + 20;
        int panelHeight = (BAR_HEIGHT + BAR_SPACING) * 2 + 40;
        
        // Render UI background panel
        renderUIPanel(renderer, x - 5, y - 5, panelWidth, panelHeight);
        
        // Render health section
        renderHealthSection(renderer, x, y, player);
        
        // Render stamina section
        int staminaY = y + BAR_HEIGHT + BAR_SPACING + 20;
        renderStaminaSection(renderer, x, staminaY, player);
        
        // Render status effects
        int statusY = staminaY + BAR_HEIGHT + BAR_SPACING + 20;
        renderStatusEffects(renderer, x, statusY, player);
    }
    
    private static void renderUIPanel(ModernRenderer renderer, int x, int y, int width, int height) {
        // Background with rounded corners effect
        renderer.drawRect(x, y, width, height, UI_BACKGROUND);
        
        // Border
        renderer.drawRectOutline(x, y, width, height, UI_BORDER, 1);
        
        // Inner highlight for depth
        renderer.drawRectOutline(x + 1, y + 1, width - 2, height - 2, new Color(255, 255, 255, 30), 1);
    }
    
    private static void renderHealthSection(ModernRenderer renderer, int x, int y, Player player) {
        // Health label
        renderer.drawText("HEALTH", x, y + 12, LABEL_COLOR, labelFont);
        
        // Health value
        String healthText = player.getHealth() + " / " + player.getMaxHealth();
        renderer.drawText(healthText, x + BAR_WIDTH - 40, y + 12, TEXT_COLOR, valueFont);
        
        // Health bar background
        int barY = y + 15;
        renderer.drawRect(x, barY, BAR_WIDTH, BAR_HEIGHT, HEALTH_BG);
        
        // Health bar fill
        float healthPercent = (float) player.getHealth() / player.getMaxHealth();
        int fillWidth = (int) (BAR_WIDTH * healthPercent);
        
        if (fillWidth > 0) {
            Color healthColor;
            if (healthPercent > 0.6f) {
                healthColor = HEALTH_HIGH;
            } else if (healthPercent > 0.3f) {
                healthColor = HEALTH_MED;
            } else {
                healthColor = HEALTH_LOW;
            }
            
            // Gradient fill for modern look
            Color healthColorDark = new Color(
                Math.max(0, healthColor.getRed() - 40),
                Math.max(0, healthColor.getGreen() - 40),
                Math.max(0, healthColor.getBlue() - 40)
            );
            
            renderer.drawGradientRect(x + 2, barY + 2, fillWidth - 4, BAR_HEIGHT - 4, 
                healthColor, healthColorDark, false);
        }
        
        // Health bar border
        renderer.drawRectOutline(x, barY, BAR_WIDTH, BAR_HEIGHT, UI_BORDER, 1);
        
        // Shine effect on top
        renderer.setComposite(0.3f);
        renderer.drawRect(x + 2, barY + 2, BAR_WIDTH - 4, 2, Color.WHITE);
        renderer.resetComposite();
    }
    
    private static void renderStaminaSection(ModernRenderer renderer, int x, int y, Player player) {
        // Stamina label
        renderer.drawText("STAMINA", x, y + 12, LABEL_COLOR, labelFont);
        
        // Stamina value
        String staminaText = player.getStamina() + " / " + player.getMaxStamina();
        renderer.drawText(staminaText, x + BAR_WIDTH - 40, y + 12, TEXT_COLOR, valueFont);
        
        // Stamina bar background
        int barY = y + 15;
        renderer.drawRect(x, barY, BAR_WIDTH, BAR_HEIGHT, STAMINA_BG);
        
        // Stamina bar fill
        float staminaPercent = (float) player.getStamina() / player.getMaxStamina();
        int fillWidth = (int) (BAR_WIDTH * staminaPercent);
        
        if (fillWidth > 0) {
            Color staminaColorDark = new Color(
                Math.max(0, STAMINA_FILL.getRed() - 40),
                Math.max(0, STAMINA_FILL.getGreen() - 40),
                Math.max(0, STAMINA_FILL.getBlue() - 40)
            );
            
            renderer.drawGradientRect(x + 2, barY + 2, fillWidth - 4, BAR_HEIGHT - 4, 
                STAMINA_FILL, staminaColorDark, false);
        }
        
        // Stamina bar border
        renderer.drawRectOutline(x, barY, BAR_WIDTH, BAR_HEIGHT, UI_BORDER, 1);
        
        // Shine effect on top
        renderer.setComposite(0.3f);
        renderer.drawRect(x + 2, barY + 2, BAR_WIDTH - 4, 2, Color.WHITE);
        renderer.resetComposite();
    }
    
    private static void renderStatusEffects(ModernRenderer renderer, int x, int y, Player player) {
        int currentY = y;
        
        if (player.isInvulnerable()) {
            renderer.drawText("⚡ INVULNERABLE", x, currentY + 10, new Color(255, 255, 100), statusFont);
            currentY += 12;
        }
        
        if (!player.canRun()) {
            renderer.drawText("💨 EXHAUSTED", x, currentY + 10, new Color(255, 150, 100), statusFont);
            currentY += 12;
        }
        
        // Add running indicator
        if (player.isRunning()) {
            renderer.drawText("🏃 RUNNING", x, currentY + 10, new Color(100, 255, 100), statusFont);
        }
    }
    
    public static void renderGameInfo(ModernRenderer renderer, String gameName, String version, int fps, boolean connected) {
        int x = renderer.getWidth() - 150;
        int y = 20;
        
        // Game info panel
        renderUIPanel(renderer, x - 5, y - 5, 140, 60);
        
        renderer.drawText(gameName + " " + version, x, y + 12, TEXT_COLOR, valueFont);
        renderer.drawText("FPS: " + fps, x, y + 25, LABEL_COLOR, valueFont);
        
        Color connectionColor = connected ? new Color(100, 255, 100) : new Color(255, 100, 100);
        String connectionText = connected ? "● CONNECTED" : "● DISCONNECTED";
        renderer.drawText(connectionText, x, y + 38, connectionColor, valueFont);
    }
    
    public static void renderCrosshair(ModernRenderer renderer, int centerX, int centerY) {
        Color crosshairColor = new Color(255, 255, 255, 150);
        int size = 8;
        int thickness = 2;
        
        // Horizontal line
        renderer.drawRect(centerX - size, centerY - thickness/2, size * 2, thickness, crosshairColor);
        // Vertical line
        renderer.drawRect(centerX - thickness/2, centerY - size, thickness, size * 2, crosshairColor);
        
        // Center dot
        renderer.drawCircle(centerX, centerY, 1, new Color(255, 255, 255, 200));
    }
    
    public static void renderMinimap(ModernRenderer renderer, Player player, int x, int y, int size) {
        if (player == null) return;
        
        // Minimap background
        renderer.drawRect(x, y, size, size, new Color(0, 0, 0, 150));
        renderer.drawRectOutline(x, y, size, size, UI_BORDER, 1);
        
        // Player position (center of minimap)
        int centerX = x + size / 2;
        int centerY = y + size / 2;
        renderer.drawCircle(centerX, centerY, 2, new Color(100, 150, 255));
        
        // Minimap label
        renderer.drawText("MAP", x + 2, y + 10, LABEL_COLOR, statusFont);
    }
}
