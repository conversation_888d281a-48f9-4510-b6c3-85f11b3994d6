package com.gmail.sync667.gougou.ui;

import com.gmail.sync667.gougou.entities.player.Player;
import com.gmail.sync667.gougou.gfx.Colours;
import com.gmail.sync667.gougou.gfx.Font;
import com.gmail.sync667.gougou.gfx.Screen;

public class HealthStaminaUI {
    
    private static final int BAR_WIDTH = 40;
    private static final int BAR_HEIGHT = 4;
    private static final int BAR_SPACING = 6;
    private static final int UI_MARGIN = 2;
    
    public static void render(Screen screen, Player player) {
        if (player == null) return;

        int x = UI_MARGIN;
        int y = UI_MARGIN;

        // Render health bar
        renderHealthBar(screen, x, y, player);

        // Render stamina bar below health bar
        renderStaminaBar(screen, x, y + BAR_HEIGHT + BAR_SPACING, player);

        // Render health and stamina text
        renderHealthText(screen, x + BAR_WIDTH + 8, y, player);
        renderStaminaText(screen, x + BAR_WIDTH + 8, y + BAR_HEIGHT + BAR_SPACING, player);

        // Render status effects
        renderStatusEffects(screen, x, y + (BAR_HEIGHT + BAR_SPACING) * 2 + 4, player);
    }
    
    private static void renderUIBackground(Screen screen, int x, int y, int width, int height) {
        // Dark semi-transparent background
        int bgColor = Colours.get(-1, 000, 000, 000);
        renderBar(screen, x, y, width, height, bgColor);

        // Border for RPG look
        int borderColor = Colours.get(-1, 333, 333, 333);
        renderBarBorder(screen, x, y, width, height, borderColor);
    }

    private static void renderHealthBar(Screen screen, int x, int y, Player player) {
        int health = player.getHealth();
        int maxHealth = player.getMaxHealth();

        // Calculate bar fill percentage
        float healthPercent = (float) health / maxHealth;
        int fillWidth = (int) (BAR_WIDTH * healthPercent);

        // Background (dark red)
        int bgColor = Colours.get(-1, 100, 000, 000);
        renderBar(screen, x, y, BAR_WIDTH, BAR_HEIGHT, bgColor);

        // Health fill (gradient from green to red based on health)
        int fillColor;
        if (healthPercent > 0.6f) {
            fillColor = Colours.get(-1, 050, 300, 050); // Green
        } else if (healthPercent > 0.3f) {
            fillColor = Colours.get(-1, 300, 300, 050); // Yellow
        } else {
            fillColor = Colours.get(-1, 300, 050, 050); // Red
        }

        if (fillWidth > 0) {
            renderBar(screen, x + 1, y + 1, fillWidth - 2, BAR_HEIGHT - 2, fillColor);
        }

        // Border
        int borderColor = Colours.get(-1, 444, 444, 444);
        renderBarBorder(screen, x, y, BAR_WIDTH, BAR_HEIGHT, borderColor);
    }
    
    private static void renderStaminaBar(Screen screen, int x, int y, Player player) {
        int stamina = player.getStamina();
        int maxStamina = player.getMaxStamina();

        // Calculate bar fill percentage
        float staminaPercent = (float) stamina / maxStamina;
        int fillWidth = (int) (BAR_WIDTH * staminaPercent);

        // Background (dark blue)
        int bgColor = Colours.get(-1, 000, 000, 100);
        renderBar(screen, x, y, BAR_WIDTH, BAR_HEIGHT, bgColor);

        // Stamina fill (blue gradient)
        int fillColor;
        if (staminaPercent > 0.5f) {
            fillColor = Colours.get(-1, 050, 150, 300); // Bright blue
        } else if (staminaPercent > 0.2f) {
            fillColor = Colours.get(-1, 050, 100, 250); // Medium blue
        } else {
            fillColor = Colours.get(-1, 050, 050, 200); // Dark blue
        }

        if (fillWidth > 0) {
            renderBar(screen, x + 1, y + 1, fillWidth - 2, BAR_HEIGHT - 2, fillColor);
        }

        // Border
        int borderColor = Colours.get(-1, 444, 444, 444);
        renderBarBorder(screen, x, y, BAR_WIDTH, BAR_HEIGHT, borderColor);
    }
    
    private static void renderBar(Screen screen, int x, int y, int width, int height, int color) {
        // Ensure color is within valid range (0-255)
        if (color < 0) color = 0;
        if (color > 255) color = 255;

        for (int dy = 0; dy < height; dy++) {
            for (int dx = 0; dx < width; dx++) {
                int screenX = x + dx;
                int screenY = y + dy;
                if (screenX >= 0 && screenX < screen.width && screenY >= 0 && screenY < screen.height) {
                    int pixelIndex = screenX + screenY * screen.width;
                    if (pixelIndex >= 0 && pixelIndex < screen.pixels.length) {
                        screen.pixels[pixelIndex] = color;
                    }
                }
            }
        }
    }
    
    private static void renderBarBorder(Screen screen, int x, int y, int width, int height, int color) {
        // Ensure color is within valid range (0-255)
        if (color < 0) color = 0;
        if (color > 255) color = 255;

        // Top and bottom borders
        for (int dx = 0; dx < width; dx++) {
            int screenX = x + dx;
            if (screenX >= 0 && screenX < screen.width) {
                // Top border
                if (y >= 0 && y < screen.height) {
                    int pixelIndex = screenX + y * screen.width;
                    if (pixelIndex >= 0 && pixelIndex < screen.pixels.length) {
                        screen.pixels[pixelIndex] = color;
                    }
                }
                // Bottom border
                int bottomY = y + height - 1;
                if (bottomY >= 0 && bottomY < screen.height) {
                    int pixelIndex = screenX + bottomY * screen.width;
                    if (pixelIndex >= 0 && pixelIndex < screen.pixels.length) {
                        screen.pixels[pixelIndex] = color;
                    }
                }
            }
        }

        // Left and right borders
        for (int dy = 0; dy < height; dy++) {
            int screenY = y + dy;
            if (screenY >= 0 && screenY < screen.height) {
                // Left border
                if (x >= 0 && x < screen.width) {
                    int pixelIndex = x + screenY * screen.width;
                    if (pixelIndex >= 0 && pixelIndex < screen.pixels.length) {
                        screen.pixels[pixelIndex] = color;
                    }
                }
                // Right border
                int rightX = x + width - 1;
                if (rightX >= 0 && rightX < screen.width) {
                    int pixelIndex = rightX + screenY * screen.width;
                    if (pixelIndex >= 0 && pixelIndex < screen.pixels.length) {
                        screen.pixels[pixelIndex] = color;
                    }
                }
            }
        }
    }
    
    private static void renderHealthLabel(Screen screen, int x, int y, Player player) {
        // Render "HP" label
        Font.render("HP", screen, x + TEXT_OFFSET_X, y + TEXT_OFFSET_Y, Colours.get(-1, 500, 050, 050), 1);

        // Render health numbers on the right side of the label
        String healthText = player.getHealth() + "/" + player.getMaxHealth();
        Font.render(healthText, screen, x + 16, y + TEXT_OFFSET_Y, Colours.get(-1, 555, 555, 555), 1);
    }

    private static void renderStaminaLabel(Screen screen, int x, int y, Player player) {
        // Render "SP" label (Stamina Points)
        Font.render("SP", screen, x + TEXT_OFFSET_X, y + TEXT_OFFSET_Y, Colours.get(-1, 050, 150, 300), 1);

        // Render stamina numbers on the right side of the label
        String staminaText = player.getStamina() + "/" + player.getMaxStamina();
        Font.render(staminaText, screen, x + 16, y + TEXT_OFFSET_Y, Colours.get(-1, 555, 555, 555), 1);
    }
    
    private static void renderStatusEffects(Screen screen, int x, int y, Player player) {
        int statusY = y;

        if (player.isInvulnerable()) {
            Font.render("INVULNERABLE", screen, x + TEXT_OFFSET_X, statusY, Colours.get(-1, 550, 550, 050), 1);
            statusY += 8; // Move to next line
        }

        // Check if player can run
        if (!player.canRun()) {
            Font.render("EXHAUSTED", screen, x + TEXT_OFFSET_X, statusY, Colours.get(-1, 550, 200, 050), 1);
        }
    }
    
    // RPG-style UI rendering with proper background
    public static void renderWithBackground(Screen screen, Player player) {
        if (player == null) return;

        // Render the RPG-style UI
        render(screen, player);
    }
    
    private static int darkenColor(int color) {
        // Simple darkening effect - reduce each color component
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;
        
        r = Math.max(0, r - 50);
        g = Math.max(0, g - 50);
        b = Math.max(0, b - 50);
        
        return (r << 16) | (g << 8) | b;
    }
}
