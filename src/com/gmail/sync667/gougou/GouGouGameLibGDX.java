package com.gmail.sync667.gougou;

import com.badlogic.gdx.ApplicationAdapter;
import com.badlogic.gdx.Gdx;
import com.badlogic.gdx.Input;
import com.badlogic.gdx.graphics.GL20;
import com.badlogic.gdx.graphics.OrthographicCamera;
import com.badlogic.gdx.graphics.Texture;
import com.badlogic.gdx.graphics.g2d.BitmapFont;
import com.badlogic.gdx.graphics.g2d.SpriteBatch;
import com.badlogic.gdx.graphics.g2d.TextureRegion;
import com.badlogic.gdx.graphics.glutils.ShapeRenderer;
import com.badlogic.gdx.math.Vector2;
import com.badlogic.gdx.utils.viewport.FitViewport;
import com.badlogic.gdx.utils.viewport.Viewport;
import com.gmail.sync667.gougou.entities.player.Player;
import com.gmail.sync667.gougou.level.Level;
import com.gmail.sync667.gougou.net.GouGouClient;
import com.gmail.sync667.gougou.net.packets.Packet00HandShakeClient;
import com.gmail.sync667.gougou.net.packets.Packet04Disconnect;

import javax.swing.*;
import java.net.InetAddress;
import java.net.UnknownHostException;

public class GouGouGameLibGDX extends ApplicationAdapter {
    
    // Game constants
    public static final int VIRTUAL_WIDTH = 320;
    public static final int VIRTUAL_HEIGHT = 240;
    public static final String NAME = "GouGou";
    public static final String VERSION = "ALPHA-0.2 LibGDX";
    public static final int PROTOCOL_VERSION = 2;
    
    // LibGDX components
    private SpriteBatch batch;
    private ShapeRenderer shapeRenderer;
    private OrthographicCamera camera;
    private Viewport viewport;
    private BitmapFont font;
    
    // Game components
    public static Player player;
    public Level level;
    public GouGouClient gougouClient;
    
    // Networking
    public String username;
    private InetAddress serverIp;
    private int port;
    
    // Game state
    private boolean running = false;
    private boolean connected = false;
    private float deltaTime = 0;
    
    // Input handling
    private boolean[] keys = new boolean[256];
    private boolean isRunning = false;
    
    @Override
    public void create() {
        System.out.println("Starting GouGou with LibGDX...");
        
        // Initialize LibGDX components
        batch = new SpriteBatch();
        shapeRenderer = new ShapeRenderer();
        font = new BitmapFont();
        font.getData().setScale(0.5f); // Scale down for pixel art style
        
        // Setup camera and viewport
        camera = new OrthographicCamera();
        viewport = new FitViewport(VIRTUAL_WIDTH, VIRTUAL_HEIGHT, camera);
        camera.position.set(VIRTUAL_WIDTH / 2f, VIRTUAL_HEIGHT / 2f, 0);
        camera.update();
        
        // Initialize game components
        level = new Level("/levels/level1.png");
        
        // Initialize networking
        initializeNetworking();
        
        running = true;
        System.out.println("LibGDX initialization complete!");
    }
    
    private void initializeNetworking() {
        // Get username
        username = JOptionPane.showInputDialog(null, "Type your username!");
        if (username == null || username.trim().isEmpty()) {
            username = "Player";
        }
        
        // Try to connect to local server first
        String defaultServerIp = "127.0.0.1";
        int defaultPort = 1332;
        
        try {
            InetAddress defaultAddress = InetAddress.getByName(defaultServerIp);
            System.out.println("Checking for local server at " + defaultServerIp + ":" + defaultPort + "...");
            
            // For now, assume server is available (we can add connection testing later)
            serverIp = defaultAddress;
            port = defaultPort;
            
            // Initialize client
            gougouClient = new GouGouClient(this, serverIp, port);
            gougouClient.start();
            
            // Send handshake
            gougouClient.sendData(new Packet00HandShakeClient(PROTOCOL_VERSION, (short) 1).getData());
            
            connected = true;
            System.out.println("Connected to server!");
            
        } catch (UnknownHostException e) {
            System.err.println("Failed to connect to server: " + e.getMessage());
            // Could show error dialog or try manual IP input
        }
    }
    
    @Override
    public void render() {
        deltaTime = Gdx.graphics.getDeltaTime();
        
        // Handle input
        handleInput();
        
        // Update game logic
        update(deltaTime);
        
        // Clear screen
        Gdx.gl.glClearColor(0.1f, 0.1f, 0.1f, 1);
        Gdx.gl.glClear(GL20.GL_COLOR_BUFFER_BIT);
        
        // Set viewport
        viewport.apply();
        batch.setProjectionMatrix(camera.combined);
        shapeRenderer.setProjectionMatrix(camera.combined);
        
        // Render game
        renderGame();
        
        // Render UI
        renderUI();
    }
    
    private void handleInput() {
        // Store previous key states
        boolean wasRunning = isRunning;
        
        // Check movement keys
        keys[Input.Keys.W] = Gdx.input.isKeyPressed(Input.Keys.W);
        keys[Input.Keys.A] = Gdx.input.isKeyPressed(Input.Keys.A);
        keys[Input.Keys.S] = Gdx.input.isKeyPressed(Input.Keys.S);
        keys[Input.Keys.D] = Gdx.input.isKeyPressed(Input.Keys.D);
        keys[Input.Keys.UP] = Gdx.input.isKeyPressed(Input.Keys.UP);
        keys[Input.Keys.LEFT] = Gdx.input.isKeyPressed(Input.Keys.LEFT);
        keys[Input.Keys.DOWN] = Gdx.input.isKeyPressed(Input.Keys.DOWN);
        keys[Input.Keys.RIGHT] = Gdx.input.isKeyPressed(Input.Keys.RIGHT);
        
        // Check running key
        isRunning = Gdx.input.isKeyPressed(Input.Keys.SHIFT_LEFT) || Gdx.input.isKeyPressed(Input.Keys.SHIFT_RIGHT);
        
        // Display controls
        if (Gdx.input.isKeyJustPressed(Input.Keys.F11)) {
            toggleFullscreen();
        }
        if (Gdx.input.isKeyJustPressed(Input.Keys.F9)) {
            showDisplayOptions();
        }
    }
    
    private void update(float deltaTime) {
        // Update level
        if (level != null) {
            level.tick();
        }
        
        // Update player movement (this will be sent to server)
        if (player != null) {
            updatePlayerMovement();
        }
    }
    
    private void updatePlayerMovement() {
        int xa = 0;
        int ya = 0;
        
        if (keys[Input.Keys.W] || keys[Input.Keys.UP]) ya++;
        if (keys[Input.Keys.S] || keys[Input.Keys.DOWN]) ya--;
        if (keys[Input.Keys.A] || keys[Input.Keys.LEFT]) xa--;
        if (keys[Input.Keys.D] || keys[Input.Keys.RIGHT]) xa++;
        
        if (xa != 0 || ya != 0) {
            // Send movement to server
            // This will be implemented when we integrate with the existing networking
        }
    }
    
    private void renderGame() {
        batch.begin();
        
        // Render level
        if (level != null) {
            renderLevel();
        }
        
        // Render entities
        renderEntities();
        
        batch.end();
    }
    
    private void renderLevel() {
        // For now, render a simple background
        // Later we'll implement proper tile rendering
        shapeRenderer.begin(ShapeRenderer.ShapeType.Filled);
        shapeRenderer.setColor(0.2f, 0.6f, 0.2f, 1); // Green background
        shapeRenderer.rect(0, 0, VIRTUAL_WIDTH, VIRTUAL_HEIGHT);
        shapeRenderer.end();
    }
    
    private void renderEntities() {
        // Render player and other entities
        if (player != null) {
            renderPlayer();
        }
        
        // Render other entities from level
        if (level != null && level.entities != null) {
            for (com.gmail.sync667.gougou.entities.Entity entity : level.entities) {
                if (entity != player) {
                    renderEntity(entity);
                }
            }
        }
    }
    
    private void renderPlayer() {
        // Simple player rendering as a colored rectangle
        shapeRenderer.begin(ShapeRenderer.ShapeType.Filled);
        shapeRenderer.setColor(0.2f, 0.2f, 0.8f, 1); // Blue player
        shapeRenderer.rect(player.x - 4, player.y - 4, 8, 8);
        shapeRenderer.end();
    }
    
    private void renderEntity(com.gmail.sync667.gougou.entities.Entity entity) {
        shapeRenderer.begin(ShapeRenderer.ShapeType.Filled);
        
        // Different colors for different entity types
        if (entity instanceof com.gmail.sync667.gougou.entities.ClientAggressiveFlower) {
            shapeRenderer.setColor(0.8f, 0.2f, 0.2f, 1); // Red flower
        } else if (entity instanceof com.gmail.sync667.gougou.entities.ClientStrawberry) {
            shapeRenderer.setColor(0.8f, 0.4f, 0.4f, 1); // Pink strawberry
        } else {
            shapeRenderer.setColor(0.5f, 0.5f, 0.5f, 1); // Gray default
        }
        
        shapeRenderer.rect(entity.x - 4, entity.y - 4, 8, 8);
        shapeRenderer.end();
    }
    
    private void renderUI() {
        // Render UI elements
        batch.begin();
        
        // Render health and stamina bars
        if (player != null) {
            renderHealthStaminaUI();
        }
        
        // Render game info
        font.draw(batch, NAME + " " + VERSION, 10, VIRTUAL_HEIGHT - 10);
        font.draw(batch, "FPS: " + Gdx.graphics.getFramesPerSecond(), 10, VIRTUAL_HEIGHT - 25);
        
        if (connected) {
            font.draw(batch, "Connected to server", 10, VIRTUAL_HEIGHT - 40);
        } else {
            font.draw(batch, "Not connected", 10, VIRTUAL_HEIGHT - 40);
        }
        
        batch.end();
    }
    
    private void renderHealthStaminaUI() {
        // Modern health and stamina bars using LibGDX
        float barWidth = 60;
        float barHeight = 8;
        float x = 10;
        float y = 50;
        
        // Health bar background
        shapeRenderer.begin(ShapeRenderer.ShapeType.Filled);
        shapeRenderer.setColor(0.3f, 0.1f, 0.1f, 0.8f);
        shapeRenderer.rect(x, y, barWidth, barHeight);
        
        // Health bar fill
        float healthPercent = (float) player.getHealth() / player.getMaxHealth();
        if (healthPercent > 0.6f) {
            shapeRenderer.setColor(0.2f, 0.8f, 0.2f, 1); // Green
        } else if (healthPercent > 0.3f) {
            shapeRenderer.setColor(0.8f, 0.8f, 0.2f, 1); // Yellow
        } else {
            shapeRenderer.setColor(0.8f, 0.2f, 0.2f, 1); // Red
        }
        shapeRenderer.rect(x + 1, y + 1, (barWidth - 2) * healthPercent, barHeight - 2);
        
        // Stamina bar background
        float staminaY = y - 15;
        shapeRenderer.setColor(0.1f, 0.1f, 0.3f, 0.8f);
        shapeRenderer.rect(x, staminaY, barWidth, barHeight);
        
        // Stamina bar fill
        float staminaPercent = (float) player.getStamina() / player.getMaxStamina();
        shapeRenderer.setColor(0.2f, 0.4f, 0.8f, 1); // Blue
        shapeRenderer.rect(x + 1, staminaY + 1, (barWidth - 2) * staminaPercent, barHeight - 2);
        
        shapeRenderer.end();
        
        // Health and stamina text
        batch.begin();
        font.draw(batch, "HP: " + player.getHealth() + "/" + player.getMaxHealth(), x, y + barHeight + 12);
        font.draw(batch, "SP: " + player.getStamina() + "/" + player.getMaxStamina(), x, staminaY + barHeight + 12);
        batch.end();
    }
    
    private void toggleFullscreen() {
        if (Gdx.graphics.isFullscreen()) {
            Gdx.graphics.setWindowedMode(800, 600);
        } else {
            Gdx.graphics.setFullscreenMode(Gdx.graphics.getDisplayMode());
        }
    }
    
    private void showDisplayOptions() {
        // Could implement a proper in-game menu later
        System.out.println("Display options - F11: Toggle Fullscreen");
    }
    
    @Override
    public void resize(int width, int height) {
        viewport.update(width, height);
    }
    
    @Override
    public void dispose() {
        if (gougouClient != null && connected) {
            gougouClient.sendData(new Packet04Disconnect(username).getData());
        }
        
        batch.dispose();
        shapeRenderer.dispose();
        font.dispose();
        
        System.out.println("LibGDX resources disposed");
    }
}
