package com.gmail.sync667.gougou.entities;

import com.gmail.sync667.gougou.level.Level;

public class ClientStrawberry extends Entity {

    private int animationTimer = 0;

    public ClientStrawberry(int entityId, Level level, int x, int y) {
        super(entityId, level);
        this.x = x;
        this.y = y;
    }

    @Override
    public void tick() {
        animationTimer++;
    }
    
    @Override
    public int getMovingDir() {
        return 0; // Strawberries don't move
    }
    
    @Override
    public void setMovingDir(int movingDir) {
        // Strawberries don't move
    }
}
