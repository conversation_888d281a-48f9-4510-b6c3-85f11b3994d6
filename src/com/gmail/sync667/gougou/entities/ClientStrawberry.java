package com.gmail.sync667.gougou.entities;

import com.gmail.sync667.gougou.gfx.Colours;
import com.gmail.sync667.gougou.gfx.Screen;
import com.gmail.sync667.gougou.level.Level;

public class ClientStrawberry extends Entity {
    
    private int animationTimer = 0;
    private int colour = Colours.get(-1, 500, 050, 550); // Red strawberry with green
    
    public ClientStrawberry(int entityId, Level level, int x, int y) {
        super(entityId, level);
        this.x = x;
        this.y = y;
    }
    
    @Override
    public void tick() {
        animationTimer++;
    }
    
    @Override
    public void render(Screen screen) {
        int xOffset = x - 4; // Center the 8x8 sprite
        int yOffset = y - 4;
        
        // Simple strawberry representation
        int strawberryColour = colour;
        
        // Add a gentle glow effect
        double pulse = Math.sin(animationTimer * 0.05) * 0.3 + 0.7;
        int glowIntensity = (int) (pulse * 100);
        strawberryColour = Colours.get(-1, 500 + glowIntensity, 050, 550);
        
        // Animate the strawberry (gentle floating effect)
        int floatOffset = (int) (Math.sin(animationTimer * 0.08) * 1);
        
        // Render a strawberry using tile 17 (or another visible tile)
        screen.render(xOffset, yOffset + floatOffset, 17, strawberryColour, 0x00, 1);

        // Add sparkle effect occasionally
        if (animationTimer % 60 < 10) { // Sparkle every second for a brief moment
            screen.render(xOffset - 2, yOffset - 2 + floatOffset, 18, Colours.get(-1, 555, 555, 555), 0x00, 1);
        }
    }
    
    @Override
    public int getMovingDir() {
        return 0; // Strawberries don't move
    }
    
    @Override
    public void setMovingDir(int movingDir) {
        // Strawberries don't move
    }
}
