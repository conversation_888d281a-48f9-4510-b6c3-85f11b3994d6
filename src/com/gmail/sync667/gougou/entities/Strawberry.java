package com.gmail.sync667.gougou.entities;

import com.gmail.sync667.gougou.entities.player.Player;
import com.gmail.sync667.gougou.gfx.Colours;
import com.gmail.sync667.gougou.gfx.Screen;
import com.gmail.sync667.gougou.level.Level;

public class <PERSON>rawberry extends Entity {
    
    private int animationTimer = 0;
    private boolean isConsumed = false;
    private int healAmount = 25;
    private int staminaAmount = 30;
    private int colour = Colours.get(-1, 500, 050, 550); // Red strawberry with green leaves
    private int respawnTimer = 0;
    private static final int RESPAWN_TIME = 1800; // 30 seconds at 60 FPS
    
    public Strawberry(int entityId, Level level, int x, int y) {
        super(entityId, level);
        this.x = x;
        this.y = y;
    }
    
    @Override
    public void tick() {
        animationTimer++;
        
        if (isConsumed) {
            respawnTimer++;
            if (respawnTimer >= RESPAWN_TIME) {
                // Respawn the strawberry
                isConsumed = false;
                respawnTimer = 0;
                System.out.println("Strawberry respawned at (" + x + ", " + y + ")");
            }
        }
    }
    
    @Override
    public void render(Screen screen) {
        if (isConsumed) {
            return; // Don't render if consumed
        }
        
        int xOffset = x - 4; // Center the 8x8 sprite
        int yOffset = y - 4;
        
        // Strawberry sprite (tile 18 = strawberry)
        int strawberryTile = 18;
        int strawberryColour = colour;
        
        // Add a gentle glow effect when ready to be picked
        if (!isConsumed) {
            // Animate with a gentle pulsing glow
            double pulse = Math.sin(animationTimer * 0.05) * 0.3 + 0.7;
            int glowIntensity = (int) (pulse * 100);
            strawberryColour = Colours.get(-1, 500 + glowIntensity, 050, 550);
        }
        
        // Animate the strawberry (gentle floating effect)
        int floatOffset = (int) (Math.sin(animationTimer * 0.08) * 1);
        
        screen.render(xOffset, yOffset + floatOffset, strawberryTile, strawberryColour, 0x00, 1);
        
        // Add sparkle effect around the strawberry
        if (animationTimer % 60 < 10) { // Sparkle every second for a brief moment
            // Small sparkle particles
            screen.render(xOffset - 2, yOffset - 2 + floatOffset, 19, Colours.get(-1, 555, 555, 555), 0x00, 1);
            screen.render(xOffset + 6, yOffset + 6 + floatOffset, 19, Colours.get(-1, 555, 555, 555), 0x00, 1);
        }
    }
    
    public void healPlayer(Player player) {
        if (!isConsumed && player != null) {
            // Heal the player
            player.heal(healAmount);
            player.restoreStamina(staminaAmount);
            
            // Mark as consumed
            isConsumed = true;
            respawnTimer = 0;
            
            System.out.println(player.getUsername() + " consumed a strawberry! +" + healAmount + " HP, +" + staminaAmount + " Stamina");
            
            // Could add particle effects or sound here
            createHealingEffect();
        }
    }
    
    private void createHealingEffect() {
        // This could spawn particle effects or play sounds
        // For now, just print a message
        System.out.println("✨ Healing effect at (" + x + ", " + y + ")");
    }
    
    public boolean isAvailable() {
        return !isConsumed;
    }
    
    public int getTimeUntilRespawn() {
        if (!isConsumed) return 0;
        return RESPAWN_TIME - respawnTimer;
    }
    
    public float getRespawnProgress() {
        if (!isConsumed) return 1.0f;
        return (float) respawnTimer / RESPAWN_TIME;
    }
    
    @Override
    public int getMovingDir() {
        return 0; // Strawberries don't move
    }
    
    @Override
    public void setMovingDir(int movingDir) {
        // Strawberries don't move
    }
    
    // Getters and setters for customization
    public int getHealAmount() { return healAmount; }
    public void setHealAmount(int amount) { this.healAmount = amount; }
    public int getStaminaAmount() { return staminaAmount; }
    public void setStaminaAmount(int amount) { this.staminaAmount = amount; }
    
    public void forceRespawn() {
        isConsumed = false;
        respawnTimer = 0;
    }
    
    public void setRespawnTime(int ticks) {
        // Allow customization of respawn time
        // Note: This would require making RESPAWN_TIME non-final
    }
}
