package com.gmail.sync667.gougou.entities;

import com.gmail.sync667.gougou.entities.player.Player;
import com.gmail.sync667.gougou.gfx.Colours;
import com.gmail.sync667.gougou.gfx.Screen;
import com.gmail.sync667.gougou.level.Level;

public class AggressiveFlower extends Entity {
    
    private int attackCooldown = 0;
    private int animationTimer = 0;
    private boolean isAttacking = false;
    private int detectionRange = 24; // 3 tiles
    private int attackRange = 8; // 1 tile
    private int damage = 15;
    private int colour = Colours.get(-1, 500, 050, 005); // Red flower
    
    public AggressiveFlower(int entityId, Level level, int x, int y) {
        super(entityId, level);
        this.x = x;
        this.y = y;
    }
    
    @Override
    public void tick() {
        animationTimer++;
        
        // Decrease attack cooldown
        if (attackCooldown > 0) {
            attackCooldown--;
        }
        
        // Check for nearby player
        Player player = findNearbyPlayer();
        if (player != null) {
            int dx = player.x - this.x;
            int dy = player.y - this.y;
            double distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance <= attackRange && attackCooldown <= 0) {
                // Attack the player
                isAttacking = true;
                attackCooldown = 120; // 2 seconds cooldown at 60 FPS
            } else if (distance <= detectionRange) {
                // Show aggressive animation when player is nearby
                isAttacking = false;
            } else {
                isAttacking = false;
            }
        } else {
            isAttacking = false;
        }
    }
    
    @Override
    public void render(Screen screen) {
        int xOffset = x - 4; // Center the 8x8 sprite
        int yOffset = y - 4;
        
        // Base flower sprite (tile 16 = flower base)
        int baseTile = 16;
        int flowerColour = colour;
        
        // Change color when attacking
        if (isAttacking) {
            flowerColour = Colours.get(-1, 550, 050, 005); // Brighter red when attacking
            baseTile = 17; // Different sprite when attacking
        } else if (findNearbyPlayer() != null) {
            // Slightly different color when player is in detection range
            flowerColour = Colours.get(-1, 520, 050, 005);
        }
        
        // Animate the flower (simple bobbing effect)
        int bobOffset = (int) (Math.sin(animationTimer * 0.1) * 1);
        
        screen.render(xOffset, yOffset + bobOffset, baseTile, flowerColour, 0x00, 1);
    }
    
    public void attackPlayer(Player player) {
        if (attackCooldown <= 0 && !player.isInvulnerable()) {
            player.takeDamage(damage);
            attackCooldown = 120; // 2 seconds cooldown
            isAttacking = true;
            System.out.println("Aggressive flower attacked " + player.getUsername() + " for " + damage + " damage!");
        }
    }
    
    private Player findNearbyPlayer() {
        if (level == null) return null;
        
        for (Entity entity : level.entities) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                int dx = Math.abs(player.x - this.x);
                int dy = Math.abs(player.y - this.y);
                double distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance <= detectionRange) {
                    return player;
                }
            }
        }
        return null;
    }
    
    @Override
    public int getMovingDir() {
        return 0; // Flowers don't move
    }
    
    @Override
    public void setMovingDir(int movingDir) {
        // Flowers don't move
    }
    
    // Getters for customization
    public int getDamage() { return damage; }
    public void setDamage(int damage) { this.damage = damage; }
    public int getDetectionRange() { return detectionRange; }
    public void setDetectionRange(int range) { this.detectionRange = range; }
    public int getAttackRange() { return attackRange; }
    public void setAttackRange(int range) { this.attackRange = range; }
}
