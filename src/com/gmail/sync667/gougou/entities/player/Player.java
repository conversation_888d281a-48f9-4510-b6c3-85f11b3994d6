package com.gmail.sync667.gougou.entities.player;

import com.gmail.sync667.gougou.GouGou;
import com.gmail.sync667.gougou.InputHandler;
import com.gmail.sync667.gougou.entities.Mob;
import com.gmail.sync667.gougou.level.Level;

public class Player extends Mob {

    private final InputHandler input;
    private final int colour = Colours.get(-1, 111, 145, 543);
    protected boolean isSwimming = false;
    private int tickCount = 0;
    public int scale = 1;
    private final String username;

    // Health and Stamina System (client-side display only)
    public static final int MAX_HEALTH = 100;
    public static final int MAX_STAMINA = 100;
    private int health = MAX_HEALTH;
    private int stamina = MAX_STAMINA;
    private boolean isRunning = false;
    private int invulnerabilityTimer = 0; // For visual effects only

    public Player(int entityId, Level level, int x, int y, InputHandler input, String username) {
        super(entityId, level, "<PERSON>racz", x, y, 1);
        this.input = input;
        this.username = username;
    }

    @Override
    public void tick() {
        int xa = 0;
        int ya = 0;

        if (GouGou.player == null) {
            return;
        }

        if (entityId == GouGou.player.getEntityId()) {
            // Check for running (shift key) - we'll need to add this to InputHandler
            isRunning = input.shift != null && input.shift.isPressed() && canRun();

            if (input.up.isPressed()) {
                ya--;
            }
            if (input.down.isPressed()) {
                ya++;
            }
            if (input.left.isPressed()) {
                xa--;
            }
            if (input.right.isPressed()) {
                xa++;
            }

            if (xa != 0 || ya != 0) {
                // Running logic (server manages actual stamina consumption)
                if (isRunning) {
                    speed = 2; // Double speed when running
                } else {
                    speed = 1; // Normal speed
                }

                move(xa, ya);
                isMoving = true;
            } else {
                isMoving = false;
                speed = 1; // Reset speed when not moving
            }

            // Swimming logic
            if (level.getTile(this.x >> 3, this.y >> 3).getId() == 3) {
                isSwimming = true;
            }
            if (isSwimming && level.getTile(this.x >> 3, this.y >> 3).getId() != 3) {
                isSwimming = false;
            }

            // Update visual effects only (server manages actual health/stamina)
            updateVisualEffects();

            tickCount++;
            this.scale = 1;
        }
    }

    private void updateVisualEffects() {
        // Decrease invulnerability timer for visual effects
        if (invulnerabilityTimer > 0) {
            invulnerabilityTimer--;
        }
    }

    @Override
    public void render(Screen screen) {
        int xTile = 0;
        int yTile = 28;
        int walkingSpeed = 4;
        int flipTop = (numSteps >> walkingSpeed) & 1;
        int flipBottom = (numSteps >> walkingSpeed) & 1;

        if (movingDir == 1) {
            xTile += 2;
        } else if (movingDir > 1) {
            xTile += 4 + ((numSteps >> walkingSpeed) & 1) * 2;
            flipTop = (movingDir - 1) % 2;
        }

        int modifier = 8 * scale;
        int xOffset = x - modifier / 2;
        int yOffset = y - modifier / 2 - 4;

        if (isSwimming) {
            int waterColour = 0;
            yOffset += 4;
            if (tickCount % 60 < 15) {
                waterColour = Colours.get(-1, -1, 225, -1);
            } else if (15 < -tickCount % 60 && tickCount % 60 < 30) {
                yOffset -= 1;
                waterColour = Colours.get(-1, 255, 115, -1);
            } else if (30 <= tickCount % 60 && tickCount % 60 < 45) {
                waterColour = Colours.get(-1, 115, -1, 225);
            } else {
                yOffset -= 1;
                waterColour = Colours.get(-1, 225, 115, -1);
            }
            screen.render(xOffset, yOffset + 3, 0 + 27 * 32, waterColour, 0x00, 1);
            screen.render(xOffset + 8, yOffset + 3, 0 + 27 * 32, waterColour, 0x01, 1);
        }

        screen.render(xOffset + (modifier * flipTop), yOffset, xTile + yTile * 32, colour, flipTop, scale);
        screen.render(xOffset + modifier - (modifier * flipTop), yOffset, xTile + 1 + yTile * 32, colour, flipTop,
                scale);
        if (!isSwimming) {
            screen.render(xOffset + (modifier * flipBottom), yOffset + modifier, xTile + (yTile + 1) * 32, colour,
                    flipBottom, scale);
            screen.render(xOffset + modifier - (modifier * flipBottom), yOffset + modifier, (xTile + 1) + (yTile + 1)
                    * 32, colour, flipBottom, scale);
        }
        if (username != null) {
            Font.render(username, screen, xOffset - ((username.length() - 1) / 2 * 8), yOffset - 10,
                    Colours.get(-1, -1, -1, 555), 1);
        }
    }

    @Override
    public boolean hasCollided(int xa, int ya) {
        int xMin = 0;
        int xMax = 7;
        int yMin = 3;
        int yMax = 7;

        for (int x = xMin; x < xMax; x++) {
            if (isSolidTile(xa, ya, x, yMin)) {
                return true;
            }
        }
        for (int x = xMin; x < xMax; x++) {
            if (isSolidTile(xa, ya, x, yMax)) {
                return true;
            }
        }
        for (int y = yMin; y < yMin; y++) {
            if (isSolidTile(xa, ya, yMin, y)) {
                return true;
            }
        }
        for (int y = yMin; y < yMax; y++) {
            if (isSolidTile(xa, ya, yMax, y)) {
                return true;
            }
        }
        return false;
    }

    public String getUsername() {
        return username;
    }

    public void setSpeed(int speed) {
        this.speed = speed;
    }

    // Health and Stamina Methods (client-side display only)
    public void updateHealth(int newHealth) {
        this.health = newHealth;
        if (health < 0) health = 0;
        if (health > MAX_HEALTH) health = MAX_HEALTH;

        // Visual effect for taking damage
        if (newHealth < health) {
            invulnerabilityTimer = 60; // Visual invulnerability effect
        }
    }

    public void updateStamina(int newStamina) {
        this.stamina = newStamina;
        if (stamina < 0) stamina = 0;
        if (stamina > MAX_STAMINA) stamina = MAX_STAMINA;
    }

    public boolean canRun() {
        return stamina > 10; // Need at least 10 stamina to run
    }

    // Getters
    public int getHealth() { return health; }
    public int getMaxHealth() { return MAX_HEALTH; }
    public int getStamina() { return stamina; }
    public int getMaxStamina() { return MAX_STAMINA; }
    public boolean isInvulnerable() { return invulnerabilityTimer > 0; }
    public boolean isRunning() { return isRunning; }

}
