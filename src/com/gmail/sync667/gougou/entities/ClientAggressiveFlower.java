package com.gmail.sync667.gougou.entities;

import com.gmail.sync667.gougou.gfx.Colours;
import com.gmail.sync667.gougou.gfx.Screen;
import com.gmail.sync667.gougou.level.Level;

public class ClientAggressiveFlower extends Entity {
    
    private int animationTimer = 0;
    private int colour = Colours.get(-1, 500, 050, 005); // Red flower
    
    public ClientAggressiveFlower(int entityId, Level level, int x, int y) {
        super(entityId, level);
        this.x = x;
        this.y = y;
    }
    
    @Override
    public void tick() {
        animationTimer++;
    }
    
    @Override
    public void render(Screen screen) {
        int xOffset = x - 4; // Center the 8x8 sprite
        int yOffset = y - 4;

        // Simple flower representation using basic tiles
        int flowerColour = colour;

        // Animate the flower (simple bobbing effect)
        int bobOffset = (int) (Math.sin(animationTimer * 0.1) * 1);

        // Use tile 16 (or another visible tile) for flower - try different tiles to find a good one
        screen.render(xOffset, yOffset + bobOffset, 16, flowerColour, 0x00, 1);
    }
    
    @Override
    public int getMovingDir() {
        return 0; // Flowers don't move
    }
    
    @Override
    public void setMovingDir(int movingDir) {
        // Flowers don't move
    }
}
