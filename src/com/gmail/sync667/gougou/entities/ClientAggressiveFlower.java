package com.gmail.sync667.gougou.entities;

import com.gmail.sync667.gougou.level.Level;

public class ClientAggressiveFlower extends Entity {

    private int animationTimer = 0;

    public ClientAggressiveFlower(int entityId, Level level, int x, int y) {
        super(entityId, level);
        this.x = x;
        this.y = y;
    }

    @Override
    public void tick() {
        animationTimer++;
    }
    
    @Override
    public int getMovingDir() {
        return 0; // Flowers don't move
    }
    
    @Override
    public void setMovingDir(int movingDir) {
        // Flowers don't move
    }
}
