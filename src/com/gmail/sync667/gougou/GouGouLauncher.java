package com.gmail.sync667.gougou;

import com.badlogic.gdx.backends.lwjgl3.Lwjgl3Application;
import com.badlogic.gdx.backends.lwjgl3.Lwjgl3ApplicationConfiguration;

public class GouGouLauncher {
    
    public static void main(String[] args) {
        System.out.println("Starting GouGou with LibGDX backend...");
        
        // Create LibGDX configuration
        Lwjgl3ApplicationConfiguration config = new Lwjgl3ApplicationConfiguration();
        
        // Window settings
        config.setTitle("GouGou ALPHA-0.2 LibGDX");
        config.setWindowedMode(800, 600);
        config.setResizable(true);
        
        // Graphics settings
        config.useVsync(true);
        config.setForegroundFPS(60);
        config.setIdleFPS(30);
        
        // Window icon (if available)
        // config.setWindowIcon("icon.png");
        
        // Enable fullscreen toggle
        config.setFullscreenMode(null);
        
        // Audio settings
        config.setAudioConfig(512, 512, 9);
        
        // Create and start the application
        try {
            new Lwjgl3Application(new GouGouGameLibGDX(), config);
        } catch (Exception e) {
            System.err.println("Failed to start LibGDX application: " + e.getMessage());
            e.printStackTrace();
            
            // Fallback to old system
            System.out.println("Falling back to legacy rendering system...");
            new GouGou().start();
        }
    }
}
