package com.gmail.sync667.gougou.world;

import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class ProceduralMapGenerator {
    
    public enum TerrainType {
        WATER(0, new Color(65, 105, 225), "Water"),
        SAND(1, new Color(238, 203, 173), "Sand"),
        GRASS(2, new Color(34, 139, 34), "Grass"),
        FOREST(3, new Color(0, 100, 0), "Forest"),
        MOUNTAIN(4, new Color(139, 137, 137), "Mountain"),
        DESERT(5, new Color(238, 203, 173), "Desert"),
        SWAMP(6, new Color(107, 142, 35), "Swamp"),
        SNOW(7, new Color(255, 250, 250), "Snow");
        
        public final int id;
        public final Color color;
        public final String name;
        
        TerrainType(int id, Color color, String name) {
            this.id = id;
            this.color = color;
            this.name = name;
        }
    }
    
    public enum ResourceType {
        TREE(new Color(34, 139, 34), "Tree"),
        ROCK(new Color(105, 105, 105), "Rock"),
        BERRY_BUSH(new Color(220, 20, 60), "Berry Bush"),
        FLOWER(new Color(255, 182, 193), "Flower"),
        CRYSTAL(new Color(138, 43, 226), "Crystal"),
        CACTUS(new Color(0, 128, 0), "Cactus"),
        MUSHROOM(new Color(160, 82, 45), "Mushroom"),
        RUINS(new Color(169, 169, 169), "Ruins");
        
        public final Color color;
        public final String name;
        
        ResourceType(Color color, String name) {
            this.color = color;
            this.name = name;
        }
    }
    
    public static class MapTile {
        public TerrainType terrain;
        public ResourceType resource;
        public boolean hasResource;
        public int elevation;
        public double temperature;
        public double moisture;
        
        public MapTile(TerrainType terrain) {
            this.terrain = terrain;
            this.hasResource = false;
            this.elevation = 0;
            this.temperature = 0.5;
            this.moisture = 0.5;
        }
    }
    
    private int width, height;
    private long seed;
    private Random random;
    private MapTile[][] map;
    
    // Noise parameters
    private static final double ELEVATION_SCALE = 0.01;
    private static final double TEMPERATURE_SCALE = 0.005;
    private static final double MOISTURE_SCALE = 0.008;
    private static final double RESOURCE_DENSITY = 0.15;
    
    public ProceduralMapGenerator(int width, int height, long seed) {
        this.width = width;
        this.height = height;
        this.seed = seed;
        this.random = new Random(seed);
        this.map = new MapTile[width][height];
    }
    
    public MapTile[][] generateMap() {
        System.out.println("Generating procedural map " + width + "x" + height + " with seed: " + seed);
        
        // Step 1: Generate elevation map
        double[][] elevationMap = generateNoiseMap(ELEVATION_SCALE);
        
        // Step 2: Generate temperature map
        double[][] temperatureMap = generateNoiseMap(TEMPERATURE_SCALE);
        
        // Step 3: Generate moisture map
        double[][] moistureMap = generateNoiseMap(MOISTURE_SCALE);
        
        // Step 4: Determine terrain types based on elevation, temperature, and moisture
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                double elevation = elevationMap[x][y];
                double temperature = temperatureMap[x][y];
                double moisture = moistureMap[x][y];
                
                TerrainType terrain = determineTerrainType(elevation, temperature, moisture);
                map[x][y] = new MapTile(terrain);
                map[x][y].elevation = (int) (elevation * 100);
                map[x][y].temperature = temperature;
                map[x][y].moisture = moisture;
            }
        }
        
        // Step 5: Add resources
        generateResources();
        
        // Step 6: Create paths and structures
        generatePaths();
        generateStructures();
        
        System.out.println("Map generation complete!");
        return map;
    }
    
    private double[][] generateNoiseMap(double scale) {
        double[][] noiseMap = new double[width][height];
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                // Simple Perlin-like noise using multiple octaves
                double value = 0;
                double amplitude = 1;
                double frequency = scale;
                
                for (int octave = 0; octave < 4; octave++) {
                    value += noise(x * frequency, y * frequency) * amplitude;
                    amplitude *= 0.5;
                    frequency *= 2;
                }
                
                // Normalize to 0-1 range
                noiseMap[x][y] = Math.max(0, Math.min(1, (value + 1) / 2));
            }
        }
        
        return noiseMap;
    }
    
    private double noise(double x, double y) {
        // Simple noise function - can be replaced with proper Perlin noise
        int xi = (int) Math.floor(x);
        int yi = (int) Math.floor(y);
        
        double xf = x - xi;
        double yf = y - yi;
        
        double n00 = dotGridGradient(xi, yi, x, y);
        double n10 = dotGridGradient(xi + 1, yi, x, y);
        double n01 = dotGridGradient(xi, yi + 1, x, y);
        double n11 = dotGridGradient(xi + 1, yi + 1, x, y);
        
        double nx0 = interpolate(n00, n10, xf);
        double nx1 = interpolate(n01, n11, xf);
        
        return interpolate(nx0, nx1, yf);
    }
    
    private double dotGridGradient(int ix, int iy, double x, double y) {
        Random r = new Random(seed + ix * 374761393L + iy * 668265263L);
        double angle = r.nextDouble() * 2 * Math.PI;
        double gradX = Math.cos(angle);
        double gradY = Math.sin(angle);
        
        double dx = x - ix;
        double dy = y - iy;
        
        return dx * gradX + dy * gradY;
    }
    
    private double interpolate(double a, double b, double t) {
        // Smooth interpolation
        t = t * t * (3.0 - 2.0 * t);
        return a + t * (b - a);
    }
    
    private TerrainType determineTerrainType(double elevation, double temperature, double moisture) {
        // Water at low elevations
        if (elevation < 0.3) {
            return TerrainType.WATER;
        }
        
        // Beach/sand near water
        if (elevation < 0.35) {
            return TerrainType.SAND;
        }
        
        // Mountains at high elevations
        if (elevation > 0.8) {
            if (temperature < 0.3) {
                return TerrainType.SNOW;
            }
            return TerrainType.MOUNTAIN;
        }
        
        // Biome determination based on temperature and moisture
        if (temperature < 0.3) {
            // Cold regions
            if (moisture > 0.6) {
                return TerrainType.SWAMP;
            }
            return TerrainType.SNOW;
        } else if (temperature > 0.7) {
            // Hot regions
            if (moisture < 0.3) {
                return TerrainType.DESERT;
            }
            return TerrainType.SAND;
        } else {
            // Temperate regions
            if (moisture > 0.6) {
                return TerrainType.FOREST;
            } else if (moisture > 0.4) {
                return TerrainType.GRASS;
            } else {
                return TerrainType.SAND;
            }
        }
    }
    
    private void generateResources() {
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                MapTile tile = map[x][y];
                
                // Skip water tiles
                if (tile.terrain == TerrainType.WATER) continue;
                
                // Determine resource probability based on terrain
                double resourceChance = getResourceChance(tile.terrain);
                
                if (random.nextDouble() < resourceChance) {
                    tile.resource = getRandomResource(tile.terrain);
                    tile.hasResource = true;
                }
            }
        }
    }
    
    private double getResourceChance(TerrainType terrain) {
        switch (terrain) {
            case FOREST: return RESOURCE_DENSITY * 1.5;
            case GRASS: return RESOURCE_DENSITY;
            case MOUNTAIN: return RESOURCE_DENSITY * 0.8;
            case DESERT: return RESOURCE_DENSITY * 0.3;
            case SWAMP: return RESOURCE_DENSITY * 1.2;
            case SAND: return RESOURCE_DENSITY * 0.5;
            case SNOW: return RESOURCE_DENSITY * 0.4;
            default: return 0;
        }
    }
    
    private ResourceType getRandomResource(TerrainType terrain) {
        List<ResourceType> possibleResources = new ArrayList<>();
        
        switch (terrain) {
            case FOREST:
                possibleResources.add(ResourceType.TREE);
                possibleResources.add(ResourceType.BERRY_BUSH);
                possibleResources.add(ResourceType.FLOWER);
                possibleResources.add(ResourceType.MUSHROOM);
                break;
            case GRASS:
                possibleResources.add(ResourceType.FLOWER);
                possibleResources.add(ResourceType.BERRY_BUSH);
                possibleResources.add(ResourceType.ROCK);
                break;
            case MOUNTAIN:
                possibleResources.add(ResourceType.ROCK);
                possibleResources.add(ResourceType.CRYSTAL);
                possibleResources.add(ResourceType.RUINS);
                break;
            case DESERT:
                possibleResources.add(ResourceType.CACTUS);
                possibleResources.add(ResourceType.ROCK);
                possibleResources.add(ResourceType.RUINS);
                break;
            case SWAMP:
                possibleResources.add(ResourceType.MUSHROOM);
                possibleResources.add(ResourceType.TREE);
                break;
            case SAND:
                possibleResources.add(ResourceType.ROCK);
                break;
            case SNOW:
                possibleResources.add(ResourceType.CRYSTAL);
                possibleResources.add(ResourceType.ROCK);
                break;
        }
        
        if (possibleResources.isEmpty()) {
            return ResourceType.ROCK; // Default
        }
        
        return possibleResources.get(random.nextInt(possibleResources.size()));
    }
    
    private void generatePaths() {
        // Simple path generation - connect random points
        int numPaths = 3 + random.nextInt(5);
        
        for (int i = 0; i < numPaths; i++) {
            int startX = random.nextInt(width);
            int startY = random.nextInt(height);
            int endX = random.nextInt(width);
            int endY = random.nextInt(height);
            
            createPath(startX, startY, endX, endY);
        }
    }
    
    private void createPath(int startX, int startY, int endX, int endY) {
        // Simple line drawing algorithm
        int dx = Math.abs(endX - startX);
        int dy = Math.abs(endY - startY);
        int x = startX;
        int y = startY;
        int n = 1 + dx + dy;
        int x_inc = (endX > startX) ? 1 : -1;
        int y_inc = (endY > startY) ? 1 : -1;
        int error = dx - dy;
        
        dx *= 2;
        dy *= 2;
        
        for (; n > 0; --n) {
            if (x >= 0 && x < width && y >= 0 && y < height) {
                // Clear resources on path
                if (map[x][y].terrain != TerrainType.WATER) {
                    map[x][y].hasResource = false;
                    // Optionally change terrain to a path type
                }
            }
            
            if (error > 0) {
                x += x_inc;
                error -= dy;
            } else {
                y += y_inc;
                error += dx;
            }
        }
    }
    
    private void generateStructures() {
        // Generate some ruins and special structures
        int numStructures = 2 + random.nextInt(4);
        
        for (int i = 0; i < numStructures; i++) {
            int x = random.nextInt(width - 10) + 5;
            int y = random.nextInt(height - 10) + 5;
            
            // Create a small structure
            for (int dx = -2; dx <= 2; dx++) {
                for (int dy = -2; dy <= 2; dy++) {
                    int nx = x + dx;
                    int ny = y + dy;
                    
                    if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                        if (random.nextDouble() < 0.6) {
                            map[nx][ny].resource = ResourceType.RUINS;
                            map[nx][ny].hasResource = true;
                        }
                    }
                }
            }
        }
    }
    
    // Getters
    public MapTile getTile(int x, int y) {
        if (x >= 0 && x < width && y >= 0 && y < height) {
            return map[x][y];
        }
        return new MapTile(TerrainType.WATER); // Default for out of bounds
    }
    
    public int getWidth() { return width; }
    public int getHeight() { return height; }
    public long getSeed() { return seed; }
}
