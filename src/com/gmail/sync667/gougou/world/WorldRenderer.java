package com.gmail.sync667.gougou.world;

import com.gmail.sync667.gougou.entities.player.Player;
import com.gmail.sync667.gougou.gfx.ModernRenderer;
import com.gmail.sync667.gougou.world.ProceduralMapGenerator.MapTile;
import com.gmail.sync667.gougou.world.ProceduralMapGenerator.ResourceType;
import com.gmail.sync667.gougou.world.ProceduralMapGenerator.TerrainType;

import java.awt.Color;

public class WorldRenderer {
    
    private static final int TILE_SIZE = 8; // Size of each tile in pixels
    private static final int CHUNK_SIZE = 32; // Size of chunks for optimization
    
    private ProceduralMapGenerator mapGenerator;
    private int viewportWidth, viewportHeight;
    
    // Rendering optimization
    private int lastCameraX = -1, lastCameraY = -1;
    private boolean needsRedraw = true;
    
    public WorldRenderer(int viewportWidth, int viewportHeight) {
        this.viewportWidth = viewportWidth;
        this.viewportHeight = viewportHeight;
    }
    
    public void setMapGenerator(ProceduralMapGenerator mapGenerator) {
        this.mapGenerator = mapGenerator;
        this.needsRedraw = true;
    }
    
    public void render(ModernRenderer renderer, Player player) {
        if (mapGenerator == null || player == null) {
            renderDefaultBackground(renderer);
            return;
        }
        
        // Calculate camera position (center on player)
        int cameraX = player.x - viewportWidth / 2;
        int cameraY = player.y - viewportHeight / 2;
        
        // Check if we need to redraw
        if (cameraX != lastCameraX || cameraY != lastCameraY) {
            needsRedraw = true;
            lastCameraX = cameraX;
            lastCameraY = cameraY;
        }
        
        if (needsRedraw) {
            renderWorld(renderer, cameraX, cameraY);
            needsRedraw = false;
        }
    }
    
    private void renderWorld(ModernRenderer renderer, int cameraX, int cameraY) {
        // Calculate which tiles are visible
        int startTileX = Math.max(0, cameraX / TILE_SIZE);
        int startTileY = Math.max(0, cameraY / TILE_SIZE);
        int endTileX = Math.min(mapGenerator.getWidth() - 1, (cameraX + viewportWidth) / TILE_SIZE + 1);
        int endTileY = Math.min(mapGenerator.getHeight() - 1, (cameraY + viewportHeight) / TILE_SIZE + 1);
        
        // Render terrain layer
        for (int tileX = startTileX; tileX <= endTileX; tileX++) {
            for (int tileY = startTileY; tileY <= endTileY; tileY++) {
                MapTile tile = mapGenerator.getTile(tileX, tileY);
                
                int screenX = tileX * TILE_SIZE - cameraX;
                int screenY = tileY * TILE_SIZE - cameraY;
                
                // Skip tiles outside screen bounds
                if (screenX + TILE_SIZE < 0 || screenX >= viewportWidth || 
                    screenY + TILE_SIZE < 0 || screenY >= viewportHeight) {
                    continue;
                }
                
                renderTerrain(renderer, tile, screenX, screenY);
            }
        }
        
        // Render resource layer
        for (int tileX = startTileX; tileX <= endTileX; tileX++) {
            for (int tileY = startTileY; tileY <= endTileY; tileY++) {
                MapTile tile = mapGenerator.getTile(tileX, tileY);
                
                if (tile.hasResource) {
                    int screenX = tileX * TILE_SIZE - cameraX;
                    int screenY = tileY * TILE_SIZE - cameraY;
                    
                    // Skip tiles outside screen bounds
                    if (screenX + TILE_SIZE < 0 || screenX >= viewportWidth || 
                        screenY + TILE_SIZE < 0 || screenY >= viewportHeight) {
                        continue;
                    }
                    
                    renderResource(renderer, tile, screenX, screenY);
                }
            }
        }
        
        // Render grid for debugging (optional)
        if (false) { // Set to true to enable grid
            renderGrid(renderer, cameraX, cameraY, startTileX, startTileY, endTileX, endTileY);
        }
    }
    
    private void renderTerrain(ModernRenderer renderer, MapTile tile, int x, int y) {
        Color terrainColor = tile.terrain.color;
        
        // Add some variation based on elevation and moisture
        int variation = (int) ((tile.elevation - 50) * 0.3 + (tile.moisture - 0.5) * 20);
        terrainColor = adjustColor(terrainColor, variation);
        
        // Render base terrain
        renderer.drawRect(x, y, TILE_SIZE, TILE_SIZE, terrainColor);
        
        // Add terrain-specific details
        addTerrainDetails(renderer, tile, x, y);
    }
    
    private void addTerrainDetails(ModernRenderer renderer, MapTile tile, int x, int y) {
        switch (tile.terrain) {
            case WATER:
                // Add water animation effect
                if (System.currentTimeMillis() % 1000 < 500) {
                    Color highlight = new Color(100, 150, 255, 100);
                    renderer.drawRect(x + 1, y + 1, TILE_SIZE - 2, 1, highlight);
                }
                break;
                
            case FOREST:
                // Add tree-like pattern
                Color darkGreen = new Color(0, 80, 0);
                renderer.drawRect(x + 2, y + 2, 2, 2, darkGreen);
                renderer.drawRect(x + 4, y + 4, 2, 2, darkGreen);
                break;
                
            case MOUNTAIN:
                // Add rocky texture
                Color lightGray = new Color(180, 180, 180);
                renderer.drawRect(x + 1, y + 1, 1, 1, lightGray);
                renderer.drawRect(x + 4, y + 3, 1, 1, lightGray);
                renderer.drawRect(x + 6, y + 5, 1, 1, lightGray);
                break;
                
            case DESERT:
                // Add sand dune pattern
                Color lightSand = new Color(255, 228, 196);
                renderer.drawRect(x + 2, y + 6, 4, 1, lightSand);
                break;
                
            case SWAMP:
                // Add murky water spots
                Color murkWater = new Color(85, 107, 47, 150);
                renderer.drawRect(x + 1, y + 5, 2, 2, murkWater);
                renderer.drawRect(x + 5, y + 2, 2, 2, murkWater);
                break;
                
            case SNOW:
                // Add snow sparkle
                if (System.currentTimeMillis() % 2000 < 100) {
                    Color sparkle = new Color(255, 255, 255);
                    renderer.drawRect(x + 3, y + 2, 1, 1, sparkle);
                    renderer.drawRect(x + 6, y + 5, 1, 1, sparkle);
                }
                break;
        }
    }
    
    private void renderResource(ModernRenderer renderer, MapTile tile, int x, int y) {
        Color resourceColor = tile.resource.color;
        
        switch (tile.resource) {
            case TREE:
                // Render tree trunk and leaves
                Color trunk = new Color(101, 67, 33);
                Color leaves = new Color(34, 139, 34);
                renderer.drawRect(x + 3, y + 4, 2, 4, trunk);
                renderer.drawRect(x + 1, y + 1, 6, 4, leaves);
                break;
                
            case ROCK:
                // Render rock with shading
                Color darkRock = new Color(85, 85, 85);
                renderer.drawRect(x + 2, y + 3, 4, 3, resourceColor);
                renderer.drawRect(x + 2, y + 5, 4, 1, darkRock);
                break;
                
            case BERRY_BUSH:
                // Render bush with berries
                Color bush = new Color(0, 100, 0);
                Color berries = new Color(220, 20, 60);
                renderer.drawRect(x + 1, y + 2, 6, 4, bush);
                renderer.drawRect(x + 2, y + 3, 1, 1, berries);
                renderer.drawRect(x + 4, y + 2, 1, 1, berries);
                renderer.drawRect(x + 5, y + 4, 1, 1, berries);
                break;
                
            case FLOWER:
                // Render flower
                Color stem = new Color(0, 128, 0);
                renderer.drawRect(x + 3, y + 4, 2, 3, stem);
                renderer.drawRect(x + 2, y + 2, 4, 3, resourceColor);
                break;
                
            case CRYSTAL:
                // Render crystal with glow effect
                renderer.drawRect(x + 2, y + 2, 4, 4, resourceColor);
                if (System.currentTimeMillis() % 1500 < 300) {
                    Color glow = new Color(200, 100, 255, 100);
                    renderer.drawRect(x + 1, y + 1, 6, 6, glow);
                }
                break;
                
            case CACTUS:
                // Render cactus
                Color cactusGreen = new Color(0, 128, 0);
                renderer.drawRect(x + 3, y + 2, 2, 5, cactusGreen);
                renderer.drawRect(x + 1, y + 3, 2, 2, cactusGreen);
                renderer.drawRect(x + 5, y + 4, 2, 2, cactusGreen);
                break;
                
            case MUSHROOM:
                // Render mushroom
                Color mushroomStem = new Color(245, 245, 220);
                Color mushroomCap = new Color(160, 82, 45);
                renderer.drawRect(x + 3, y + 4, 2, 3, mushroomStem);
                renderer.drawRect(x + 1, y + 2, 6, 3, mushroomCap);
                // Add spots
                Color spots = new Color(255, 255, 255);
                renderer.drawRect(x + 2, y + 3, 1, 1, spots);
                renderer.drawRect(x + 5, y + 2, 1, 1, spots);
                break;
                
            case RUINS:
                // Render ruins
                Color stone = new Color(169, 169, 169);
                Color darkStone = new Color(105, 105, 105);
                renderer.drawRect(x + 1, y + 3, 2, 4, stone);
                renderer.drawRect(x + 4, y + 2, 3, 5, stone);
                renderer.drawRect(x + 1, y + 6, 2, 1, darkStone);
                renderer.drawRect(x + 4, y + 6, 3, 1, darkStone);
                break;
        }
    }
    
    private void renderGrid(ModernRenderer renderer, int cameraX, int cameraY, 
                           int startTileX, int startTileY, int endTileX, int endTileY) {
        Color gridColor = new Color(255, 255, 255, 50);
        
        // Vertical lines
        for (int tileX = startTileX; tileX <= endTileX; tileX++) {
            int screenX = tileX * TILE_SIZE - cameraX;
            renderer.drawRect(screenX, 0, 1, viewportHeight, gridColor);
        }
        
        // Horizontal lines
        for (int tileY = startTileY; tileY <= endTileY; tileY++) {
            int screenY = tileY * TILE_SIZE - cameraY;
            renderer.drawRect(0, screenY, viewportWidth, 1, gridColor);
        }
    }
    
    private void renderDefaultBackground(ModernRenderer renderer) {
        // Render a simple gradient background when no map is available
        Color topColor = new Color(135, 206, 235); // Sky blue
        Color bottomColor = new Color(34, 139, 34); // Forest green
        renderer.drawGradientRect(0, 0, viewportWidth, viewportHeight, topColor, bottomColor, false);
    }
    
    private Color adjustColor(Color baseColor, int variation) {
        int r = Math.max(0, Math.min(255, baseColor.getRed() + variation));
        int g = Math.max(0, Math.min(255, baseColor.getGreen() + variation));
        int b = Math.max(0, Math.min(255, baseColor.getBlue() + variation));
        return new Color(r, g, b);
    }
    
    public void forceRedraw() {
        needsRedraw = true;
    }
    
    // Get tile at world coordinates
    public MapTile getTileAtWorldPos(int worldX, int worldY) {
        if (mapGenerator == null) return null;
        int tileX = worldX / TILE_SIZE;
        int tileY = worldY / TILE_SIZE;
        return mapGenerator.getTile(tileX, tileY);
    }
}
