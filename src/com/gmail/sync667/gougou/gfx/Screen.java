package com.gmail.sync667.gougou.gfx;

// Legacy stub - not used in modern graphics system
public class Screen {
    public int width, height;
    public int[] pixels;
    private int xOffset, yOffset;

    public Screen(int width, int height, SpriteSheet sheet) {
        this.width = width;
        this.height = height;
        this.pixels = new int[width * height];
    }

    public void setOffset(int xOffset, int yOffset) {
        this.xOffset = xOffset;
        this.yOffset = yOffset;
    }

    public void render(int x, int y, int tile, int color, int flip, int scale) {
        // Stub - not used
    }
}
