package com.gmail.sync667.gougou.gfx;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.VolatileImage;

public class ModernRenderer {
    
    private int width, height;
    private BufferedImage backBuffer;
    private Graphics2D g2d;
    private GraphicsConfiguration gc;
    private VolatileImage volatileImage;
    
    // Modern rendering settings
    private boolean antiAliasing = false; // Keep pixel art crisp
    private boolean hardwareAcceleration = true;
    
    public ModernRenderer(int width, int height) {
        this.width = width;
        this.height = height;
        
        // Get graphics configuration for hardware acceleration
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        GraphicsDevice gd = ge.getDefaultScreenDevice();
        gc = gd.getDefaultConfiguration();
        
        initializeBuffers();
    }
    
    private void initializeBuffers() {
        // Create hardware-accelerated volatile image
        if (hardwareAcceleration && gc != null) {
            volatileImage = gc.createCompatibleVolatileImage(width, height, Transparency.OPAQUE);
        }
        
        // Create software fallback buffer
        backBuffer = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        setupGraphics();
    }
    
    private void setupGraphics() {
        if (volatileImage != null) {
            g2d = volatileImage.createGraphics();
        } else {
            g2d = backBuffer.createGraphics();
        }
        
        // Set rendering hints for better quality
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);
        
        if (antiAliasing) {
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        } else {
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_OFF);
        }
        
        // For pixel art, use nearest neighbor scaling
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR);
        g2d.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_SPEED);
        g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_SPEED);
    }
    
    public Graphics2D getGraphics() {
        // Check if volatile image is still valid
        if (volatileImage != null) {
            int validation = volatileImage.validate(gc);
            if (validation == VolatileImage.IMAGE_INCOMPATIBLE) {
                // Recreate the volatile image
                volatileImage = gc.createCompatibleVolatileImage(width, height, Transparency.OPAQUE);
                setupGraphics();
            }
            return volatileImage.createGraphics();
        }
        return backBuffer.createGraphics();
    }
    
    public void clear(Color color) {
        Graphics2D g = getGraphics();
        g.setColor(color);
        g.fillRect(0, 0, width, height);
        g.dispose();
    }
    
    public void drawRect(int x, int y, int width, int height, Color color) {
        Graphics2D g = getGraphics();
        g.setColor(color);
        g.fillRect(x, y, width, height);
        g.dispose();
    }
    
    public void drawRectOutline(int x, int y, int width, int height, Color color, int thickness) {
        Graphics2D g = getGraphics();
        g.setColor(color);
        g.setStroke(new BasicStroke(thickness));
        g.drawRect(x, y, width, height);
        g.dispose();
    }
    
    public void drawText(String text, int x, int y, Color color, java.awt.Font font) {
        Graphics2D g = getGraphics();
        g.setColor(color);
        g.setFont(font);

        // Enable text antialiasing for better readability
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        g.drawString(text, x, y);
        g.dispose();
    }
    
    public void drawImage(BufferedImage image, int x, int y) {
        Graphics2D g = getGraphics();
        g.drawImage(image, x, y, null);
        g.dispose();
    }
    
    public void drawImageScaled(BufferedImage image, int x, int y, int width, int height) {
        Graphics2D g = getGraphics();
        g.drawImage(image, x, y, width, height, null);
        g.dispose();
    }
    
    public void drawGradientRect(int x, int y, int width, int height, Color color1, Color color2, boolean horizontal) {
        Graphics2D g = getGraphics();
        
        GradientPaint gradient;
        if (horizontal) {
            gradient = new GradientPaint(x, y, color1, x + width, y, color2);
        } else {
            gradient = new GradientPaint(x, y, color1, x, y + height, color2);
        }
        
        g.setPaint(gradient);
        g.fillRect(x, y, width, height);
        g.dispose();
    }
    
    public void drawCircle(int centerX, int centerY, int radius, Color color) {
        Graphics2D g = getGraphics();
        g.setColor(color);
        g.fillOval(centerX - radius, centerY - radius, radius * 2, radius * 2);
        g.dispose();
    }
    
    public void setComposite(float alpha) {
        if (g2d != null) {
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
        }
    }
    
    public void resetComposite() {
        if (g2d != null) {
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1.0f));
        }
    }
    
    public BufferedImage getImage() {
        if (volatileImage != null) {
            // Copy volatile image to buffered image for display
            BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = result.createGraphics();
            g.drawImage(volatileImage, 0, 0, null);
            g.dispose();
            return result;
        }
        return backBuffer;
    }
    
    public void resize(int newWidth, int newHeight) {
        if (this.width != newWidth || this.height != newHeight) {
            this.width = newWidth;
            this.height = newHeight;
            
            // Dispose old resources
            if (g2d != null) {
                g2d.dispose();
            }
            if (volatileImage != null) {
                volatileImage.flush();
            }
            
            // Recreate buffers
            initializeBuffers();
        }
    }
    
    public void dispose() {
        if (g2d != null) {
            g2d.dispose();
        }
        if (volatileImage != null) {
            volatileImage.flush();
        }
        if (backBuffer != null) {
            backBuffer.flush();
        }
    }
    
    // Getters
    public int getWidth() { return width; }
    public int getHeight() { return height; }
    
    // Settings
    public void setAntiAliasing(boolean enabled) {
        this.antiAliasing = enabled;
        setupGraphics();
    }
    
    public void setHardwareAcceleration(boolean enabled) {
        this.hardwareAcceleration = enabled;
        if (!enabled && volatileImage != null) {
            volatileImage.flush();
            volatileImage = null;
            setupGraphics();
        }
    }
}
