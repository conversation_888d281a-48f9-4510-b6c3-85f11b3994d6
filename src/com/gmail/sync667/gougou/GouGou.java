package com.gmail.sync667.gougou;

import java.awt.BorderLayout;
import java.awt.Canvas;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferStrategy;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferInt;
import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

import javax.swing.JFrame;
import javax.swing.JOptionPane;

import com.gmail.sync667.gougou.display.DisplayManager;
import com.gmail.sync667.gougou.entities.player.Player;
import com.gmail.sync667.gougou.graphics.ModernRenderer2D;
import com.gmail.sync667.gougou.graphics.ModernUI;
import com.gmail.sync667.gougou.graphics.TileRenderer;
import com.gmail.sync667.gougou.graphics.TextureManager;
import com.gmail.sync667.gougou.level.Level;
import com.gmail.sync667.gougou.net.GouGouClient;
import com.gmail.sync667.gougou.net.packets.Packet00HandShakeClient;
import com.gmail.sync667.gougou.net.packets.Packet04Disconnect;

public class GouGou extends Canvas implements Runnable {

    private static final long serialVersionUID = 1L;

    // Use DisplayManager constants for base resolution
    public static final int WIDTH = DisplayManager.BASE_WIDTH;
    public static final int HEIGHT = DisplayManager.BASE_HEIGHT;
    public static final String NAME = "GouGou";
    public static final String VERSION = "ALPHA-0.3 High Density Graphics";
    public static final int PROTOCOL_VERSION = 2;

    public JFrame frame;
    public DisplayManager displayManager;

    public boolean running = false;
    public int ticksCount = 0;

    // Modern graphics system
    private ModernRenderer2D modernRenderer;
    private ModernUI modernUI;
    private TileRenderer tileRenderer;
    private TextureManager textureManager;

    public InputHandler input;
    public WindowHandler windowHandler;

    public static GouGouClient gougouClient;

    public String username;
    public InetAddress serverIp;
    public int port;
    public Level level;
    public static Player player;

    public GouGou() {
        frame = new JFrame(NAME);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setLayout(new BorderLayout());
        frame.add(this, BorderLayout.CENTER);

        // Initialize display manager with default settings
        displayManager = new DisplayManager(frame, this);
        displayManager.applyDisplayMode();

        // Make canvas focusable for input
        setFocusable(true);
        requestFocus();
    }

    public void init() {
        // Initialize modern graphics system
        System.out.println("Initializing modern graphics system...");

        // Initialize texture manager
        textureManager = TextureManager.getInstance();

        // Initialize modern renderer
        modernRenderer = new ModernRenderer2D(WIDTH, HEIGHT);

        // Initialize tile renderer
        tileRenderer = new TileRenderer(modernRenderer);

        // Initialize modern UI
        modernUI = new ModernUI(modernRenderer);

        // Initialize input and level
        input = new InputHandler(this);
        level = new Level("/levels/level1.png");

        System.out.println("Modern graphics system initialized successfully!");

        username = JOptionPane.showInputDialog(this, "Type your username!");

        // First, try to connect to local server
        String defaultServerIp = "127.0.0.1";
        int defaultPort = 1332;

        try {
            InetAddress defaultAddress = InetAddress.getByName(defaultServerIp);
            System.out.println("Checking for local server at " + defaultServerIp + ":" + defaultPort + "...");

            if (testServerConnection(defaultAddress, defaultPort)) {
                // Local server found!
                System.out.println("Local server found! Connecting to " + defaultServerIp + ":" + defaultPort);
                JOptionPane.showMessageDialog(this,
                    "Local server found!\nConnecting to " + defaultServerIp + ":" + defaultPort,
                    "Server Found",
                    JOptionPane.INFORMATION_MESSAGE);
                serverIp = defaultAddress;
                port = defaultPort;
            } else {
                // No local server, ask user for server IP
                System.out.println("No local server found. Asking user for server address...");
                String inputServerIp = JOptionPane.showInputDialog(this,
                    "No local server found at " + defaultServerIp + ":" + defaultPort +
                    "\nPlease enter server IP address (format: ip:port):");

                if (inputServerIp == null || inputServerIp.trim().isEmpty()) {
                    JOptionPane.showMessageDialog(this, "No server address provided. Exiting.", "Error", JOptionPane.ERROR_MESSAGE);
                    System.exit(0);
                }

                String[] iSIP = inputServerIp.split(":");
                if (iSIP.length != 2) {
                    JOptionPane.showMessageDialog(this, "Invalid format! Use ip:port format.", "Error", JOptionPane.ERROR_MESSAGE);
                    System.exit(0);
                }

                try {
                    serverIp = InetAddress.getByName(iSIP[0]);
                    port = Integer.valueOf(iSIP[1]);
                } catch (UnknownHostException e) {
                    JOptionPane.showMessageDialog(this, "Invalid IP address!", "Error", JOptionPane.ERROR_MESSAGE);
                    System.exit(0);
                } catch (NumberFormatException e) {
                    JOptionPane.showMessageDialog(this, "Invalid port number!", "Error", JOptionPane.ERROR_MESSAGE);
                    System.exit(0);
                }
            }
        } catch (UnknownHostException e) {
            // Fallback to asking user
            String inputServerIp = JOptionPane.showInputDialog(this, "Type server ip address (format: ip:port):");
            String[] iSIP = inputServerIp.split(":");
            try {
                serverIp = InetAddress.getByName(iSIP[0]);
                port = Integer.valueOf(iSIP[1]);
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(this, "Error connecting to server!", "Error", JOptionPane.ERROR_MESSAGE);
                System.exit(0);
            }
        }

        gougouClient = new GouGouClient(this, serverIp, port);
        gougouClient.start();

        gougouClient.sendData(new Packet00HandShakeClient(PROTOCOL_VERSION, (short) 1).getData());

        windowHandler = new WindowHandler(this);

    }

    // Removed old procedural world system - now using modern tile system

    public synchronized void start() {
        running = true;
        new Thread(this).start();
    }

    public synchronized void stop() {
        gougouClient.sendData(new Packet04Disconnect(player.entityId, null).getData());
        running = false;
    }

    @Override
    public void run() {
        long lastTime = System.nanoTime();
        double nsPerTick = 1000000000D / 60D;

        int ticks = 0;
        int frames = 0;

        long lastTimer = System.currentTimeMillis();
        double delta = 0;

        init();

        while (running) {
            long now = System.nanoTime();
            delta += (now - lastTime) / nsPerTick;
            lastTime = now;
            boolean shouldRender = true;

            while (delta >= 1) {
                ticks++;
                tick();
                delta -= 1;
                shouldRender = true;
            }

            try {
                Thread.sleep(2);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (shouldRender) {
                frames++;
                render();
            }

            if (System.currentTimeMillis() - lastTimer >= 1000) {
                lastTimer += 1000;
                String displayInfo = displayManager.getCurrentMode().description;
                if (displayManager.isFullscreen()) {
                    displayInfo += " (Fullscreen)";
                }
                frame.setTitle(NAME + " " + VERSION + " - " + displayInfo + " - " + ticks + " ticks, " + frames + " fps");
                frames = 0;
                ticks = 0;
            }
        }
    }

    public void tick() {
        ticksCount++;
        level.tick();
    }

    public void render() {
        BufferStrategy bs = getBufferStrategy();
        if (bs == null) {
            createBufferStrategy(3);
            return;
        }

        // Always use modern renderer
        renderModern();

        // Render the final image to screen
        Graphics g = bs.getDrawGraphics();

        // Clear the screen with black background
        g.setColor(Color.BLACK);
        g.fillRect(0, 0, getWidth(), getHeight());

        // Calculate scaling and centering for proper aspect ratio
        int canvasWidth = getWidth();
        int canvasHeight = getHeight();

        // Calculate the scale to fit the image while maintaining aspect ratio
        double scaleX = (double) canvasWidth / WIDTH;
        double scaleY = (double) canvasHeight / HEIGHT;
        double scale = Math.min(scaleX, scaleY);

        // Calculate the size and position to center the image
        int scaledWidth = (int) (WIDTH * scale);
        int scaledHeight = (int) (HEIGHT * scale);
        int x = (canvasWidth - scaledWidth) / 2;
        int y = (canvasHeight - scaledHeight) / 2;

        // Use nearest neighbor scaling for pixel art
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);

        // Draw the modern renderer image
        BufferedImage renderImage = modernRenderer.getImage();
        g2d.drawImage(renderImage, x, y, scaledWidth, scaledHeight, null);

        g.dispose();
        bs.show();
    }

    private void renderModern() {
        // Begin modern rendering frame
        modernRenderer.beginFrame();

        // Set camera to follow player
        if (player != null) {
            int cameraX = player.x - WIDTH / 2;
            int cameraY = player.y - HEIGHT / 2;
            modernRenderer.setCamera(cameraX, cameraY);
        }

        // Update animated tiles
        if (tileRenderer != null) {
            tileRenderer.update();
        }

        // Render world
        renderModernWorld();

        // Render entities
        renderModernEntities();

        // Render UI (not affected by camera)
        if (modernUI != null && player != null) {
            modernUI.render(player);
            modernUI.renderGameInfo(NAME, VERSION, 60, gougouClient != null);
            modernUI.renderMinimap(player, 0, 0, 60); // Position will be calculated inside minimap method
        }

        // End modern rendering frame
        modernRenderer.endFrame();
    }

    private void renderModernWorld() {
        // For now, render a simple test world
        if (tileRenderer != null) {
            // Generate a simple test level
            TileRenderer.TileType[][] testLevel = tileRenderer.generateSimpleLevel(50, 40);
            tileRenderer.renderTileMap(testLevel, 0, 0);
        }
    }

    private void renderModernEntities() {
        // Render player
        if (player != null) {
            modernRenderer.drawTexture("player", player.x - 4, player.y - 4);
        }

        // Render other entities (thread-safe copy)
        if (level != null && level.entities != null) {
            // Create a copy to avoid ConcurrentModificationException
            java.util.List<com.gmail.sync667.gougou.entities.Entity> entitiesCopy;
            synchronized (level.entities) {
                entitiesCopy = new java.util.ArrayList<>(level.entities);
            }

            for (com.gmail.sync667.gougou.entities.Entity entity : entitiesCopy) {
                if (entity != player && entity != null) {
                    String textureName = "grass"; // Default

                    if (entity instanceof com.gmail.sync667.gougou.entities.ClientAggressiveFlower) {
                        textureName = "flower";
                    } else if (entity instanceof com.gmail.sync667.gougou.entities.ClientStrawberry) {
                        textureName = "strawberry";
                    }

                    modernRenderer.drawTexture(textureName, entity.x - 4, entity.y - 4);
                }
            }
        }
    }

    // Legacy renderer removed - using modern graphics only

    /**
     * Test if a server is running at the specified address and port
     * @param address Server IP address
     * @param port Server port
     * @return true if server responds, false otherwise
     */
    private boolean testServerConnection(InetAddress address, int port) {
        try (DatagramSocket testSocket = new DatagramSocket()) {
            // Set a short timeout for the test
            testSocket.setSoTimeout(2000); // 2 seconds timeout

            // Create a simple test packet (handshake)
            Packet00HandShakeClient testPacket = new Packet00HandShakeClient(PROTOCOL_VERSION, (short) 1);
            byte[] data = testPacket.getData();

            DatagramPacket packet = new DatagramPacket(data, data.length, address, port);
            testSocket.send(packet);

            // Try to receive a response
            byte[] responseData = new byte[1024];
            DatagramPacket responsePacket = new DatagramPacket(responseData, responseData.length);
            testSocket.receive(responsePacket);

            // If we get here, server responded
            return true;

        } catch (SocketTimeoutException e) {
            // Server didn't respond in time
            return false;
        } catch (IOException e) {
            // Connection failed
            return false;
        }
    }

    public static GouGouClient getClient() {
        return gougouClient;
    }

    public static void main(String[] args) {
        new GouGou().start();
    }

}
