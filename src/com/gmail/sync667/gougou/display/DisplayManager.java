package com.gmail.sync667.gougou.display;

import java.awt.*;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import javax.swing.JFrame;
import javax.swing.JOptionPane;

public class DisplayManager implements KeyListener {
    
    // Base game resolution (internal rendering resolution) - Increased for higher pixel density
    public static final int BASE_WIDTH = 320;
    public static final int BASE_HEIGHT = 240; // 4:3 aspect ratio - 2x pixel density
    
    // Display modes - Updated for higher pixel density
    public enum DisplayMode {
        WINDOWED_1X(BASE_WIDTH, BASE_HEIGHT, 1, "320x240 (1x) - High Density"),
        WINDOWED_2X(BASE_WIDTH * 2, BASE_HEIGHT * 2, 2, "640x480 (2x)"),
        WINDOWED_3X(BASE_WIDTH * 3, BASE_HEIGHT * 3, 3, "960x720 (3x)"),
        WINDOWED_4X(BASE_WIDTH * 4, BASE_HEIGHT * 4, 4, "1280x960 (4x)"),
        WINDOWED_5X(BASE_WIDTH * 5, BASE_HEIGHT * 5, 5, "1600x1200 (5x)"),
        WINDOWED_6X(BASE_WIDTH * 6, BASE_HEIGHT * 6, 6, "1920x1440 (6x)"),
        FULLSCREEN(0, 0, 0, "Fullscreen");
        
        public final int width;
        public final int height;
        public final int scale;
        public final String description;
        
        DisplayMode(int width, int height, int scale, String description) {
            this.width = width;
            this.height = height;
            this.scale = scale;
            this.description = description;
        }
    }
    
    private JFrame frame;
    private Canvas canvas;
    private DisplayMode currentMode;
    private DisplayMode previousWindowedMode;
    private GraphicsDevice graphicsDevice;
    private boolean isFullscreen = false;
    
    // Screen information
    private Dimension screenSize;
    private int maxScale;
    
    public DisplayManager(JFrame frame, Canvas canvas) {
        this.frame = frame;
        this.canvas = canvas;
        this.graphicsDevice = GraphicsEnvironment.getLocalGraphicsEnvironment().getDefaultScreenDevice();
        this.screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        
        // Calculate maximum scale that fits on screen
        this.maxScale = Math.min(screenSize.width / BASE_WIDTH, screenSize.height / BASE_HEIGHT);
        
        // Start with a reasonable default scale
        this.currentMode = getDefaultDisplayMode();
        this.previousWindowedMode = currentMode;
        
        // Add key listener for display shortcuts
        canvas.addKeyListener(this);
        canvas.setFocusable(true);
        
        System.out.println("Display Manager initialized:");
        System.out.println("Screen resolution: " + screenSize.width + "x" + screenSize.height);
        System.out.println("Maximum scale: " + maxScale + "x");
        System.out.println("Default mode: " + currentMode.description);
    }
    
    /**
     * Get the best default display mode based on screen size
     */
    private DisplayMode getDefaultDisplayMode() {
        if (maxScale >= 6) return DisplayMode.WINDOWED_6X;
        if (maxScale >= 5) return DisplayMode.WINDOWED_5X;
        if (maxScale >= 4) return DisplayMode.WINDOWED_4X;
        if (maxScale >= 3) return DisplayMode.WINDOWED_3X;
        if (maxScale >= 2) return DisplayMode.WINDOWED_2X;
        return DisplayMode.WINDOWED_1X;
    }
    
    /**
     * Apply the current display mode to the window
     */
    public void applyDisplayMode() {
        if (currentMode == DisplayMode.FULLSCREEN) {
            setFullscreen(true);
        } else {
            setFullscreen(false);
            setWindowedMode(currentMode);
        }
    }
    
    /**
     * Set windowed mode with specific display mode
     */
    private void setWindowedMode(DisplayMode mode) {
        if (mode == DisplayMode.FULLSCREEN) return;
        
        isFullscreen = false;
        frame.dispose();
        frame.setUndecorated(false);
        frame.setResizable(false);
        
        // Set canvas size
        canvas.setPreferredSize(new Dimension(mode.width, mode.height));
        canvas.setMinimumSize(new Dimension(mode.width, mode.height));
        canvas.setMaximumSize(new Dimension(mode.width, mode.height));
        
        // Rebuild frame
        frame.pack();
        frame.setLocationRelativeTo(null);
        frame.setVisible(true);
        
        currentMode = mode;
        previousWindowedMode = mode;
        
        System.out.println("Switched to windowed mode: " + mode.description);
    }
    
    /**
     * Toggle fullscreen mode
     */
    public void toggleFullscreen() {
        setFullscreen(!isFullscreen);
    }
    
    /**
     * Set fullscreen mode
     */
    private void setFullscreen(boolean fullscreen) {
        if (fullscreen == isFullscreen) return;
        
        if (fullscreen) {
            // Save current windowed mode
            if (currentMode != DisplayMode.FULLSCREEN) {
                previousWindowedMode = currentMode;
            }
            
            // Switch to fullscreen
            frame.dispose();
            frame.setUndecorated(true);
            frame.setResizable(false);
            
            // Calculate best fit scale for fullscreen
            int fsScale = Math.min(screenSize.width / BASE_WIDTH, screenSize.height / BASE_HEIGHT);
            int fsWidth = BASE_WIDTH * fsScale;
            int fsHeight = BASE_HEIGHT * fsScale;
            
            canvas.setPreferredSize(new Dimension(fsWidth, fsHeight));
            canvas.setMinimumSize(new Dimension(fsWidth, fsHeight));
            canvas.setMaximumSize(new Dimension(fsWidth, fsHeight));
            
            frame.pack();
            frame.setLocationRelativeTo(null);
            
            if (graphicsDevice.isFullScreenSupported()) {
                graphicsDevice.setFullScreenWindow(frame);
            }
            
            frame.setVisible(true);
            isFullscreen = true;
            currentMode = DisplayMode.FULLSCREEN;
            
            System.out.println("Switched to fullscreen mode: " + fsWidth + "x" + fsHeight + " (" + fsScale + "x scale)");
            
        } else {
            // Exit fullscreen
            if (graphicsDevice.getFullScreenWindow() == frame) {
                graphicsDevice.setFullScreenWindow(null);
            }
            
            isFullscreen = false;
            setWindowedMode(previousWindowedMode);
        }
    }
    
    /**
     * Cycle through available display modes
     */
    public void cycleDisplayMode() {
        DisplayMode[] modes = DisplayMode.values();
        int currentIndex = 0;
        
        // Find current mode index
        for (int i = 0; i < modes.length; i++) {
            if (modes[i] == currentMode) {
                currentIndex = i;
                break;
            }
        }
        
        // Get next valid mode
        do {
            currentIndex = (currentIndex + 1) % modes.length;
        } while (modes[currentIndex] != DisplayMode.FULLSCREEN && modes[currentIndex].scale > maxScale);
        
        currentMode = modes[currentIndex];
        applyDisplayMode();
    }
    
    /**
     * Show display options dialog
     */
    public void showDisplayOptions() {
        String[] options = new String[DisplayMode.values().length];
        int selectedIndex = 0;
        
        for (int i = 0; i < DisplayMode.values().length; i++) {
            DisplayMode mode = DisplayMode.values()[i];
            if (mode == DisplayMode.FULLSCREEN || mode.scale <= maxScale) {
                options[i] = mode.description;
                if (mode == currentMode) {
                    selectedIndex = i;
                }
            } else {
                options[i] = mode.description + " (Not supported)";
            }
        }
        
        String selected = (String) JOptionPane.showInputDialog(
            frame,
            "Choose display mode:\n\nCurrent: " + currentMode.description + 
            "\nScreen: " + screenSize.width + "x" + screenSize.height +
            "\n\nKeyboard shortcuts:\nF11 - Toggle Fullscreen\nF10 - Cycle modes\nF9 - This dialog",
            "Display Options",
            JOptionPane.QUESTION_MESSAGE,
            null,
            options,
            options[selectedIndex]
        );
        
        if (selected != null) {
            for (DisplayMode mode : DisplayMode.values()) {
                if (mode.description.equals(selected)) {
                    if (mode == DisplayMode.FULLSCREEN || mode.scale <= maxScale) {
                        currentMode = mode;
                        applyDisplayMode();
                    }
                    break;
                }
            }
        }
    }
    
    // Getters
    public DisplayMode getCurrentMode() { return currentMode; }
    public boolean isFullscreen() { return isFullscreen; }
    public int getCurrentScale() { 
        if (isFullscreen) {
            return Math.min(screenSize.width / BASE_WIDTH, screenSize.height / BASE_HEIGHT);
        }
        return currentMode.scale; 
    }
    
    // KeyListener implementation for shortcuts
    @Override
    public void keyPressed(KeyEvent e) {
        switch (e.getKeyCode()) {
            case KeyEvent.VK_F11:
                toggleFullscreen();
                break;
            case KeyEvent.VK_F10:
                cycleDisplayMode();
                break;
            case KeyEvent.VK_F9:
                showDisplayOptions();
                break;
        }
    }
    
    @Override
    public void keyReleased(KeyEvent e) {}
    
    @Override
    public void keyTyped(KeyEvent e) {}
}
