package com.gmail.sync667.gougou.graphics;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.VolatileImage;

public class ModernRenderer2D {
    
    private int width, height;
    private VolatileImage backBuffer;
    private Graphics2D g2d;
    private GraphicsConfiguration gc;
    private TextureManager textureManager;
    
    // Camera system
    private int cameraX = 0, cameraY = 0;
    
    // Rendering settings
    private boolean useAntiAliasing = false; // Keep false for pixel art
    private boolean useHardwareAcceleration = true;
    
    public ModernRenderer2D(int width, int height) {
        this.width = width;
        this.height = height;
        
        // Get graphics configuration
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        GraphicsDevice gd = ge.getDefaultScreenDevice();
        gc = gd.getDefaultConfiguration();
        
        // Get texture manager
        textureManager = TextureManager.getInstance();
        
        initializeBuffer();
        System.out.println("ModernRenderer2D initialized: " + width + "x" + height);
    }
    
    private void initializeBuffer() {
        if (useHardwareAcceleration && gc != null) {
            backBuffer = gc.createCompatibleVolatileImage(width, height, Transparency.OPAQUE);
        }
        
        setupGraphics();
    }
    
    private void setupGraphics() {
        if (backBuffer != null) {
            g2d = backBuffer.createGraphics();
            
            // Set rendering hints for pixel art
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR);
            g2d.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_SPEED);
            
            if (useAntiAliasing) {
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            } else {
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_OFF);
            }
        }
    }
    
    public void beginFrame() {
        // Validate volatile image
        if (backBuffer != null) {
            int validation = backBuffer.validate(gc);
            if (validation == VolatileImage.IMAGE_INCOMPATIBLE) {
                backBuffer = gc.createCompatibleVolatileImage(width, height, Transparency.OPAQUE);
                setupGraphics();
            }
        }
        
        if (g2d != null) {
            // Clear the screen
            g2d.setColor(Color.BLACK);
            g2d.fillRect(0, 0, width, height);
        }
    }
    
    public void endFrame() {
        // Nothing special needed for end frame
    }
    
    // Camera methods
    public void setCamera(int x, int y) {
        this.cameraX = x;
        this.cameraY = y;
    }
    
    public void moveCamera(int dx, int dy) {
        this.cameraX += dx;
        this.cameraY += dy;
    }
    
    // Basic drawing methods
    public void clear(Color color) {
        if (g2d != null) {
            g2d.setColor(color);
            g2d.fillRect(0, 0, width, height);
        }
    }
    
    public void drawRect(int x, int y, int width, int height, Color color) {
        if (g2d != null) {
            g2d.setColor(color);
            g2d.fillRect(x - cameraX, y - cameraY, width, height);
        }
    }
    
    public void drawRectOutline(int x, int y, int width, int height, Color color, int thickness) {
        if (g2d != null) {
            g2d.setColor(color);
            g2d.setStroke(new BasicStroke(thickness));
            g2d.drawRect(x - cameraX, y - cameraY, width, height);
        }
    }
    
    // Texture/Sprite drawing methods
    public void drawTexture(String textureName, int x, int y) {
        BufferedImage texture = textureManager.getTexture(textureName);
        if (texture != null && g2d != null) {
            g2d.drawImage(texture, x - cameraX, y - cameraY, null);
        }
    }
    
    public void drawTexture(BufferedImage texture, int x, int y) {
        if (texture != null && g2d != null) {
            g2d.drawImage(texture, x - cameraX, y - cameraY, null);
        }
    }
    
    public void drawTextureScaled(String textureName, int x, int y, int width, int height) {
        BufferedImage texture = textureManager.getTexture(textureName);
        if (texture != null && g2d != null) {
            g2d.drawImage(texture, x - cameraX, y - cameraY, width, height, null);
        }
    }
    
    public void drawSprite(String sheetName, int spriteIndex, int x, int y) {
        BufferedImage sprite = textureManager.getSprite(sheetName, spriteIndex);
        if (sprite != null && g2d != null) {
            g2d.drawImage(sprite, x - cameraX, y - cameraY, null);
        }
    }
    
    // Tile drawing (optimized for tile-based games)
    public void drawTile(String textureName, int tileX, int tileY, int tileSize) {
        int screenX = tileX * tileSize - cameraX;
        int screenY = tileY * tileSize - cameraY;
        
        // Cull tiles outside screen
        if (screenX + tileSize < 0 || screenX >= width || 
            screenY + tileSize < 0 || screenY >= height) {
            return;
        }
        
        BufferedImage texture = textureManager.getTexture(textureName);
        if (texture != null && g2d != null) {
            g2d.drawImage(texture, screenX, screenY, null);
        }
    }
    
    // Text drawing (UI elements - not affected by camera)
    public void drawText(String text, int x, int y, Color color, Font font) {
        if (g2d != null) {
            g2d.setColor(color);

            // Create a larger font for better rendering when scaled down
            Font scaledFont = font.deriveFont(font.getSize() * 2.0f);
            g2d.setFont(scaledFont);

            // Use simple antialiasing for pixel art style
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);

            // Scale down the text position to compensate for larger font
            g2d.drawString(text, x, y + scaledFont.getSize() / 4);

            // Reset rendering hints
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);
        }
    }
    
    // UI drawing (not affected by camera)
    public void drawUIRect(int x, int y, int width, int height, Color color) {
        if (g2d != null) {
            g2d.setColor(color);
            g2d.fillRect(x, y, width, height);
        }
    }
    
    public void drawUIRectOutline(int x, int y, int width, int height, Color color, int thickness) {
        if (g2d != null) {
            g2d.setColor(color);
            g2d.setStroke(new BasicStroke(thickness));
            g2d.drawRect(x, y, width, height);
        }
    }
    
    public void drawUITexture(String textureName, int x, int y) {
        BufferedImage texture = textureManager.getTexture(textureName);
        if (texture != null && g2d != null) {
            g2d.drawImage(texture, x, y, null);
        }
    }
    
    // Gradient drawing
    public void drawGradient(int x, int y, int width, int height, Color color1, Color color2, boolean horizontal) {
        if (g2d != null) {
            GradientPaint gradient;
            if (horizontal) {
                gradient = new GradientPaint(x, y, color1, x + width, y, color2);
            } else {
                gradient = new GradientPaint(x, y, color1, x, y + height, color2);
            }
            
            g2d.setPaint(gradient);
            g2d.fillRect(x - cameraX, y - cameraY, width, height);
        }
    }
    
    // Alpha blending
    public void setAlpha(float alpha) {
        if (g2d != null) {
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
        }
    }
    
    public void resetAlpha() {
        if (g2d != null) {
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1.0f));
        }
    }
    
    // Get the final rendered image
    public BufferedImage getImage() {
        if (backBuffer != null) {
            // Convert volatile image to buffered image
            BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D resultG2d = result.createGraphics();
            resultG2d.drawImage(backBuffer, 0, 0, null);
            resultG2d.dispose();
            return result;
        }
        return null;
    }
    
    // Utility methods
    public int getWidth() { return width; }
    public int getHeight() { return height; }
    public int getCameraX() { return cameraX; }
    public int getCameraY() { return cameraY; }
    
    public void resize(int newWidth, int newHeight) {
        if (this.width != newWidth || this.height != newHeight) {
            this.width = newWidth;
            this.height = newHeight;
            
            if (g2d != null) {
                g2d.dispose();
            }
            if (backBuffer != null) {
                backBuffer.flush();
            }
            
            initializeBuffer();
        }
    }
    
    public void dispose() {
        if (g2d != null) {
            g2d.dispose();
        }
        if (backBuffer != null) {
            backBuffer.flush();
        }
        System.out.println("ModernRenderer2D disposed");
    }
}
