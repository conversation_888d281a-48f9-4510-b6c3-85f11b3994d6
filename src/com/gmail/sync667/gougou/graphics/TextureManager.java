package com.gmail.sync667.gougou.graphics;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class TextureManager {
    
    private static TextureManager instance;
    private Map<String, BufferedImage> textures;
    private Map<String, BufferedImage[]> spriteSheets;
    private GraphicsConfiguration gc;
    
    private TextureManager() {
        textures = new HashMap<>();
        spriteSheets = new HashMap<>();
        
        // Get graphics configuration for hardware acceleration
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        GraphicsDevice gd = ge.getDefaultScreenDevice();
        gc = gd.getDefaultConfiguration();
        
        loadDefaultTextures();
    }
    
    public static TextureManager getInstance() {
        if (instance == null) {
            instance = new TextureManager();
        }
        return instance;
    }
    
    private void loadDefaultTextures() {
        System.out.println("Loading default textures...");
        
        // Load main sprite sheet
        loadSpriteSheet("main", "/Sprite.png", 8, 8);
        
        // Create some basic colored textures for testing
        createColorTexture("grass", new Color(34, 139, 34), 8, 8);
        createColorTexture("water", new Color(65, 105, 225), 8, 8);
        createColorTexture("sand", new Color(238, 203, 173), 8, 8);
        createColorTexture("stone", new Color(128, 128, 128), 8, 8);
        createPlayerTexture(); // Create a proper player sprite
        createColorTexture("flower", new Color(220, 50, 50), 8, 8);
        createColorTexture("strawberry", new Color(255, 100, 150), 8, 8);
        
        System.out.println("Textures loaded successfully!");
    }
    
    public BufferedImage loadTexture(String name, String path) {
        if (textures.containsKey(name)) {
            return textures.get(name);
        }
        
        try {
            BufferedImage image = ImageIO.read(getClass().getResourceAsStream(path));
            
            // Convert to hardware-accelerated image
            BufferedImage acceleratedImage = gc.createCompatibleImage(
                image.getWidth(), image.getHeight(), Transparency.TRANSLUCENT);
            Graphics2D g2d = acceleratedImage.createGraphics();
            g2d.drawImage(image, 0, 0, null);
            g2d.dispose();
            
            textures.put(name, acceleratedImage);
            System.out.println("Loaded texture: " + name + " from " + path);
            return acceleratedImage;
            
        } catch (IOException e) {
            System.err.println("Failed to load texture: " + path + " - " + e.getMessage());
            // Return a default pink texture for missing textures
            return createColorTexture(name, Color.MAGENTA, 8, 8);
        }
    }
    
    public BufferedImage[] loadSpriteSheet(String name, String path, int tileWidth, int tileHeight) {
        if (spriteSheets.containsKey(name)) {
            return spriteSheets.get(name);
        }
        
        try {
            BufferedImage sheet = ImageIO.read(getClass().getResourceAsStream(path));
            
            int cols = sheet.getWidth() / tileWidth;
            int rows = sheet.getHeight() / tileHeight;
            BufferedImage[] sprites = new BufferedImage[cols * rows];
            
            for (int row = 0; row < rows; row++) {
                for (int col = 0; col < cols; col++) {
                    int index = row * cols + col;
                    
                    // Extract sprite
                    BufferedImage sprite = sheet.getSubimage(
                        col * tileWidth, row * tileHeight, tileWidth, tileHeight);
                    
                    // Convert to hardware-accelerated image
                    BufferedImage acceleratedSprite = gc.createCompatibleImage(
                        tileWidth, tileHeight, Transparency.TRANSLUCENT);
                    Graphics2D g2d = acceleratedSprite.createGraphics();
                    g2d.drawImage(sprite, 0, 0, null);
                    g2d.dispose();
                    
                    sprites[index] = acceleratedSprite;
                }
            }
            
            spriteSheets.put(name, sprites);
            System.out.println("Loaded sprite sheet: " + name + " (" + sprites.length + " sprites)");
            return sprites;
            
        } catch (IOException e) {
            System.err.println("Failed to load sprite sheet: " + path + " - " + e.getMessage());
            // Return array of default textures
            BufferedImage[] defaultSprites = new BufferedImage[16];
            for (int i = 0; i < defaultSprites.length; i++) {
                defaultSprites[i] = createColorTexture("default_" + i, Color.MAGENTA, tileWidth, tileHeight);
            }
            spriteSheets.put(name, defaultSprites);
            return defaultSprites;
        }
    }
    
    public BufferedImage createColorTexture(String name, Color color, int width, int height) {
        BufferedImage texture = gc.createCompatibleImage(width, height, Transparency.OPAQUE);
        Graphics2D g2d = texture.createGraphics();
        g2d.setColor(color);
        g2d.fillRect(0, 0, width, height);
        g2d.dispose();

        textures.put(name, texture);
        return texture;
    }

    private void createPlayerTexture() {
        // Create a distinctive blue player sprite
        BufferedImage playerTexture = gc.createCompatibleImage(8, 8, Transparency.OPAQUE);
        Graphics2D g2d = playerTexture.createGraphics();

        // Blue body
        g2d.setColor(new Color(50, 100, 255));
        g2d.fillRect(0, 0, 8, 8);

        // Darker blue outline
        g2d.setColor(new Color(30, 70, 200));
        g2d.drawRect(0, 0, 7, 7);

        // White center dot for visibility
        g2d.setColor(Color.WHITE);
        g2d.fillRect(3, 3, 2, 2);

        g2d.dispose();
        textures.put("player", playerTexture);
    }
    
    public BufferedImage createGradientTexture(String name, Color color1, Color color2, 
                                             int width, int height, boolean horizontal) {
        BufferedImage texture = gc.createCompatibleImage(width, height, Transparency.OPAQUE);
        Graphics2D g2d = texture.createGraphics();
        
        GradientPaint gradient;
        if (horizontal) {
            gradient = new GradientPaint(0, 0, color1, width, 0, color2);
        } else {
            gradient = new GradientPaint(0, 0, color1, 0, height, color2);
        }
        
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, width, height);
        g2d.dispose();
        
        textures.put(name, texture);
        return texture;
    }
    
    public BufferedImage getTexture(String name) {
        return textures.get(name);
    }
    
    public BufferedImage getSprite(String sheetName, int index) {
        BufferedImage[] sheet = spriteSheets.get(sheetName);
        if (sheet != null && index >= 0 && index < sheet.length) {
            return sheet[index];
        }
        return getTexture("grass"); // Default fallback
    }
    
    public BufferedImage[] getSpriteSheet(String name) {
        return spriteSheets.get(name);
    }
    
    // Utility methods for easy sprite access
    public BufferedImage getGrassTile() { return getTexture("grass"); }
    public BufferedImage getWaterTile() { return getTexture("water"); }
    public BufferedImage getSandTile() { return getTexture("sand"); }
    public BufferedImage getStoneTile() { return getTexture("stone"); }
    public BufferedImage getPlayerSprite() { return getTexture("player"); }
    public BufferedImage getFlowerSprite() { return getTexture("flower"); }
    public BufferedImage getStrawberrySprite() { return getTexture("strawberry"); }
    
    // Create animated textures
    public BufferedImage[] createAnimatedTexture(String baseName, Color[] colors, int width, int height) {
        BufferedImage[] frames = new BufferedImage[colors.length];
        for (int i = 0; i < colors.length; i++) {
            frames[i] = createColorTexture(baseName + "_" + i, colors[i], width, height);
        }
        spriteSheets.put(baseName + "_anim", frames);
        return frames;
    }
    
    // Water animation
    public BufferedImage[] getWaterAnimation() {
        if (!spriteSheets.containsKey("water_anim")) {
            Color[] waterColors = {
                new Color(65, 105, 225),
                new Color(75, 115, 235),
                new Color(85, 125, 245),
                new Color(75, 115, 235)
            };
            createAnimatedTexture("water", waterColors, 8, 8);
        }
        return spriteSheets.get("water_anim");
    }
    
    public void dispose() {
        for (BufferedImage texture : textures.values()) {
            if (texture != null) {
                texture.flush();
            }
        }
        
        for (BufferedImage[] sheet : spriteSheets.values()) {
            if (sheet != null) {
                for (BufferedImage sprite : sheet) {
                    if (sprite != null) {
                        sprite.flush();
                    }
                }
            }
        }
        
        textures.clear();
        spriteSheets.clear();
        System.out.println("TextureManager disposed");
    }
}
