package com.gmail.sync667.gougou.graphics;

import com.gmail.sync667.gougou.entities.player.Player;

import java.awt.*;

public class ModernUI {
    
    private ModernRenderer2D renderer;
    
    // UI Constants - Small base size (will be scaled up by window)
    private static final int BASE_UI_MARGIN = 4;
    private static final int BASE_BAR_WIDTH = 50;
    private static final int BASE_BAR_HEIGHT = 8;
    private static final int BASE_BAR_SPACING = 4;

    // Calculated scaled values
    private int UI_MARGIN;
    private int BAR_WIDTH;
    private int BAR_HEIGHT;
    private int BAR_SPACING;
    private float uiScale = 0; // Will be calculated once
    
    // Colors
    private static final Color UI_BACKGROUND = new Color(0, 0, 0, 180);
    private static final Color UI_BORDER = new Color(255, 255, 255, 100);
    private static final Color HEALTH_BG = new Color(60, 20, 20);
    private static final Color HEALTH_LOW = new Color(220, 50, 50);
    private static final Color HEALTH_MED = new Color(220, 180, 50);
    private static final Color HEALTH_HIGH = new Color(50, 220, 50);
    private static final Color STAMINA_BG = new Color(20, 20, 60);
    private static final Color STAMINA_FILL = new Color(50, 150, 220);
    private static final Color TEXT_COLOR = new Color(255, 255, 255);
    
    // Base font sizes - Slightly larger for better readability
    private static final int BASE_LABEL_FONT_SIZE = 9;
    private static final int BASE_VALUE_FONT_SIZE = 8;
    private static final int BASE_STATUS_FONT_SIZE = 7;

    // Scaled fonts (will be calculated)
    private Font LABEL_FONT;
    private Font VALUE_FONT;
    private Font STATUS_FONT;
    
    public ModernUI(ModernRenderer2D renderer) {
        this.renderer = renderer;
        calculateUIScale();
    }

    private void calculateUIScale() {
        // The UI needs to be much smaller since the entire image gets scaled up
        // Base resolution is 320x240, but window might be 800x600 or larger
        uiScale = 1.0f; // Start with base scale

        // Since the game scales the entire 320x240 image to fit the window,
        // we need to keep UI elements small so they don't get too big when scaled
        if (renderer.getWidth() == 320 && renderer.getHeight() == 240) {
            uiScale = 1.0f; // Keep UI at base size - window scaling will handle the rest
        }

        // Calculate scaled UI dimensions
        UI_MARGIN = (int) (BASE_UI_MARGIN * uiScale);
        BAR_WIDTH = (int) (BASE_BAR_WIDTH * uiScale);
        BAR_HEIGHT = (int) (BASE_BAR_HEIGHT * uiScale);
        BAR_SPACING = (int) (BASE_BAR_SPACING * uiScale);

        // Calculate scaled fonts
        int labelFontSize = Math.max(8, (int) (BASE_LABEL_FONT_SIZE * uiScale));
        int valueFontSize = Math.max(7, (int) (BASE_VALUE_FONT_SIZE * uiScale));
        int statusFontSize = Math.max(6, (int) (BASE_STATUS_FONT_SIZE * uiScale));

        // Use less bold fonts for better readability
        LABEL_FONT = new Font("Arial", Font.PLAIN, labelFontSize);
        VALUE_FONT = new Font("Arial", Font.PLAIN, valueFontSize);
        STATUS_FONT = new Font("Arial", Font.PLAIN, statusFontSize);

        System.out.println("UI Scale: " + uiScale + " - UI Margin: " + UI_MARGIN + " - Bar Size: " + BAR_WIDTH + "x" + BAR_HEIGHT + " - Font sizes: " + labelFontSize + "/" + valueFontSize + "/" + statusFontSize);
    }
    
    public void render(Player player) {
        if (player == null) return;

        // Only calculate scale once to stop spam
        if (uiScale == 0) {
            calculateUIScale();
        }

        // Render health and stamina bars
        renderHealthStaminaBars(player);

        // Render status effects
        renderStatusEffects(player);
    }
    
    private void renderHealthStaminaBars(Player player) {
        int x = UI_MARGIN;
        int y = UI_MARGIN;

        // Background panel - Small size (window will scale it up)
        int panelWidth = BAR_WIDTH + 15;
        int panelHeight = (BAR_HEIGHT + BAR_SPACING) * 2 + 20;
        renderer.drawUIRect(x - 4, y - 4, panelWidth, panelHeight, UI_BACKGROUND);
        renderer.drawUIRectOutline(x - 4, y - 4, panelWidth, panelHeight, UI_BORDER, 1);
        
        // Health section
        int textY = y + 8;
        renderer.drawText("HP", x, textY, TEXT_COLOR, LABEL_FONT);

        int healthBarY = y + 10;
        renderHealthBar(x, healthBarY, player);

        String healthText = player.getHealth() + "/" + player.getMaxHealth();
        renderer.drawText(healthText, x + 18, textY, TEXT_COLOR, VALUE_FONT);
        
        // Stamina section
        int staminaY = y + BAR_HEIGHT + BAR_SPACING + 12;
        int staminaTextY = staminaY + 8;
        renderer.drawText("SP", x, staminaTextY, TEXT_COLOR, LABEL_FONT);

        int staminaBarY = staminaY + 10;
        renderStaminaBar(x, staminaBarY, player);

        String staminaText = player.getStamina() + "/" + player.getMaxStamina();
        renderer.drawText(staminaText, x + 18, staminaTextY, TEXT_COLOR, VALUE_FONT);
    }
    
    private void renderHealthBar(int x, int y, Player player) {
        // Background
        renderer.drawUIRect(x, y, BAR_WIDTH, BAR_HEIGHT, HEALTH_BG);
        
        // Health fill
        float healthPercent = (float) player.getHealth() / player.getMaxHealth();
        int fillWidth = (int) (BAR_WIDTH * healthPercent);
        
        if (fillWidth > 0) {
            Color healthColor;
            if (healthPercent > 0.6f) {
                healthColor = HEALTH_HIGH;
            } else if (healthPercent > 0.3f) {
                healthColor = HEALTH_MED;
            } else {
                healthColor = HEALTH_LOW;
            }
            
            renderer.drawUIRect(x + 1, y + 1, fillWidth - 2, BAR_HEIGHT - 2, healthColor);
        }
        
        // Border
        renderer.drawUIRectOutline(x, y, BAR_WIDTH, BAR_HEIGHT, UI_BORDER, 1);
        
        // Shine effect
        renderer.setAlpha(0.3f);
        renderer.drawUIRect(x + 1, y + 1, BAR_WIDTH - 2, 2, Color.WHITE);
        renderer.resetAlpha();
    }
    
    private void renderStaminaBar(int x, int y, Player player) {
        // Background
        renderer.drawUIRect(x, y, BAR_WIDTH, BAR_HEIGHT, STAMINA_BG);
        
        // Stamina fill
        float staminaPercent = (float) player.getStamina() / player.getMaxStamina();
        int fillWidth = (int) (BAR_WIDTH * staminaPercent);
        
        if (fillWidth > 0) {
            renderer.drawUIRect(x + 1, y + 1, fillWidth - 2, BAR_HEIGHT - 2, STAMINA_FILL);
        }
        
        // Border
        renderer.drawUIRectOutline(x, y, BAR_WIDTH, BAR_HEIGHT, UI_BORDER, 1);
        
        // Shine effect
        renderer.setAlpha(0.3f);
        renderer.drawUIRect(x + 1, y + 1, BAR_WIDTH - 2, 2, Color.WHITE);
        renderer.resetAlpha();
    }
    
    private void renderStatusEffects(Player player) {
        int x = UI_MARGIN;
        int y = UI_MARGIN + (BAR_HEIGHT + BAR_SPACING) * 2 + 40;
        
        if (player.isInvulnerable()) {
            renderer.drawText("⚡ INVULNERABLE", x, y, new Color(255, 255, 100), STATUS_FONT);
            y += 12;
        }
        
        if (!player.canRun()) {
            renderer.drawText("💨 EXHAUSTED", x, y, new Color(255, 150, 100), STATUS_FONT);
            y += 12;
        }
        
        if (player.isRunning()) {
            renderer.drawText("🏃 RUNNING", x, y, new Color(100, 255, 100), STATUS_FONT);
        }
    }
    
    public void renderGameInfo(String gameName, String version, int fps, boolean connected) {
        int panelWidth = 80;
        int panelHeight = 35;
        int x = renderer.getWidth() - panelWidth - UI_MARGIN;
        int y = UI_MARGIN;

        // Info panel - Small size (window will scale it up)
        renderer.drawUIRect(x - 2, y - 2, panelWidth + 4, panelHeight + 4, UI_BACKGROUND);
        renderer.drawUIRectOutline(x - 2, y - 2, panelWidth + 4, panelHeight + 4, UI_BORDER, 1);

        int lineHeight = 9;
        int textX = x + 2;
        renderer.drawText(gameName + " " + version, textX, y + lineHeight, TEXT_COLOR, VALUE_FONT);
        renderer.drawText("FPS: " + fps, textX, y + lineHeight * 2, TEXT_COLOR, VALUE_FONT);

        Color connectionColor = connected ? new Color(100, 255, 100) : new Color(255, 100, 100);
        String connectionText = connected ? "● ON" : "● OFF";
        renderer.drawText(connectionText, textX, y + lineHeight * 3, connectionColor, VALUE_FONT);
    }
    
    public void renderMinimap(Player player, int x, int y, int size) {
        if (player == null) return;

        // Make minimap much smaller
        int minimapSize = 30; // Much smaller than the 60 passed in
        int minimapX = renderer.getWidth() - minimapSize - UI_MARGIN;
        int minimapY = UI_MARGIN + 40; // Position below game info

        // Minimap background
        renderer.drawUIRect(minimapX, minimapY, minimapSize, minimapSize, new Color(0, 0, 0, 150));
        renderer.drawUIRectOutline(minimapX, minimapY, minimapSize, minimapSize, UI_BORDER, 1);

        // Player position (center of minimap)
        int centerX = minimapX + minimapSize / 2;
        int centerY = minimapY + minimapSize / 2;
        renderer.drawUIRect(centerX - 1, centerY - 1, 2, 2, new Color(100, 150, 255));

        // Minimap label (smaller)
        renderer.drawText("MAP", minimapX + 1, minimapY + 8, TEXT_COLOR, STATUS_FONT);
    }
    
    public void renderCrosshair() {
        int centerX = renderer.getWidth() / 2;
        int centerY = renderer.getHeight() / 2;
        
        Color crosshairColor = new Color(255, 255, 255, 150);
        int size = 6;
        
        // Horizontal line
        renderer.drawUIRect(centerX - size, centerY - 1, size * 2, 2, crosshairColor);
        // Vertical line
        renderer.drawUIRect(centerX - 1, centerY - size, 2, size * 2, crosshairColor);
        
        // Center dot
        renderer.drawUIRect(centerX - 1, centerY - 1, 2, 2, new Color(255, 255, 255, 200));
    }
    
    public void renderDebugInfo(Player player) {
        if (player == null) return;
        
        int x = 10;
        int y = renderer.getHeight() - 60;
        
        // Debug panel
        renderer.drawUIRect(x - 4, y - 4, 150, 55, new Color(0, 0, 0, 200));
        renderer.drawUIRectOutline(x - 4, y - 4, 150, 55, UI_BORDER, 1);
        
        renderer.drawText("DEBUG INFO", x, y + 10, new Color(255, 255, 0), LABEL_FONT);
        renderer.drawText("Player: (" + player.x + ", " + player.y + ")", x, y + 22, TEXT_COLOR, VALUE_FONT);
        renderer.drawText("Camera: (" + renderer.getCameraX() + ", " + renderer.getCameraY() + ")", x, y + 34, TEXT_COLOR, VALUE_FONT);
        renderer.drawText("Health: " + player.getHealth() + " Stamina: " + player.getStamina(), x, y + 46, TEXT_COLOR, VALUE_FONT);
    }
}
