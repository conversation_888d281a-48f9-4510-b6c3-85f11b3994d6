package com.gmail.sync667.gougou.level;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.imageio.ImageIO;

import com.gmail.sync667.gougou.GouGou;
import com.gmail.sync667.gougou.entities.Entity;
import com.gmail.sync667.gougou.level.tiles.Tile;

public class Level {

    private byte[] tiles;
    public int width;
    public int height;
    public List<Entity> entities = new ArrayList<Entity>();
    private final String imagePath;
    private BufferedImage image;
    private boolean entitiesSpawned = false;

    public Level(String imagePath) {
        this.imagePath = imagePath;

        if (imagePath != null) {
            this.loadLevelFromFile();
        } else {

            this.width = 64;
            this.height = 64;
            tiles = new byte[width * height];
            this.generateLevel();
        }
    }

    private void loadLevelFromFile() {
        try {
            this.image = ImageIO.read(Level.class.getResource(this.imagePath));
            this.width = image.getWidth();
            this.height = image.getHeight();
            tiles = new byte[width * height];
            this.loadTiles();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void loadTiles() {
        int[] tilesColours = this.image.getRGB(0, 0, width, height, null, 0, width);
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                tileCheck:
                for (Tile t : Tile.tiles) {
                    if (t != null && t.getLevelColour() == tilesColours[x + y * width]) {
                        this.tiles[x + y * width] = t.getId();
                        break tileCheck;
                    }
                }
            }
        }
    }

    private void saveLevelToFile() {
        try {
            ImageIO.write(image, "png", new File(Level.class.getResource(this.imagePath).getFile()));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void alterTile(int x, int y, Tile newTile) {
        this.tiles[x + y * width] = newTile.getId();
        image.setRGB(x, y, newTile.getLevelColour());
    }

    public void generateLevel() {
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                if (x * y % 10 < 7) {
                    tiles[x + y * width] = Tile.GRASS.getId();
                } else {
                    tiles[x + y * width] = Tile.STONE.getId();
                }
            }
        }
    }

    public void tick() {
        if (GouGou.player == null) {
            return;
        }
        for (Entity e : entities) {
            if (e == null) {
                break;
            }
            e.tick();
        }

        // Tick all tiles
        for (Tile t : Tile.tiles) {
            if (t == null) {
                break;
            }
            t.tick();
        }
    }

    // Legacy rendering methods removed - now handled by LibGDX

    public Tile getTile(int x, int y) {
        if (0 > x || x >= width || 0 > y || y >= height) {
            return Tile.VOID;
        }

        return Tile.tiles[tiles[x + y * width]];

    }

    public void addEntity(Entity entity) {
        entities.add(entity);
        // Server now manages entity spawning
    }

    public void removeEntity(int entityId) {
        int index = 0;
        for (Entity e : entities) {
            index++;
            if (e.getEntityId() == entityId) {
                break;
            }
        }

        if (index != 0) {
            entities.remove(index);
        }
    }

    // Removed client-side entity spawning - now handled by server
}
