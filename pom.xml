<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.gmail.sync667</groupId>
    <artifactId>GouGou</artifactId>
    <version>ALPHA-0.1</version>
    <packaging>jar</packaging>

    <name>GouGou</name>
    <description>2D Game Engine</description>
    
    <developers>
        <developer>
            <id>sync667</id>
        </developer>
    </developers>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <exec.mainClass>com.gmail.sync667.gougou.GouGouLauncher</exec.mainClass>
        <gdx.version>1.12.1</gdx.version>
    </properties>

    <!-- No external dependencies needed for now -->

    <profiles>
        <profile>
            <id>client</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <exec.mainClass>com.gmail.sync667.gougou.GouGou</exec.mainClass>
            </properties>
        </profile>
        <profile>
            <id>server</id>
            <properties>
                <exec.mainClass>com.gmail.sync667.gougouserver.GouGouServer</exec.mainClass>
            </properties>
        </profile>
    </profiles>

    <build>
        <directory>${project.basedir}/target</directory>
        <finalName>${project.artifactId}-${project.version}</finalName>
            <defaultGoal>install</defaultGoal>
    		<sourceDirectory>${basedir}/src/</sourceDirectory>
    <resources>
      <!-- Data files -->
      <resource>
        <targetPath>.</targetPath>
        <filtering>false</filtering>
        <directory>${basedir}/res/</directory>
        <includes>
          <include>**/*.png</include>
          <include>**/levels/*.png</include>
        </includes>
      </resource>
    </resources>
    
        <plugins>
            <plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.2.2</version>
				<configuration>
					<archive>
						<manifest>
							<mainClass>com.gmail.sync667.gougou.GouGou</mainClass>
						</manifest>
					</archive>
				</configuration>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>${exec.mainClass}</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>